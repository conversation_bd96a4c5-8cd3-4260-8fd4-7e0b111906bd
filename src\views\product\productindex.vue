<template>
    <el-row>
        <div v-if="insertHtml1">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i>课程管理</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col style="padding-bottom: 0px;">
                <el-form :inline="true">
                    <el-select v-model="condition.campusId" placeholder="请选择校区">
                        <el-option v-for="item in campusList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                    <el-form-item class="handle-del mr10">
                        <el-cascader placeholder="选择课程类别:" popper-class="theder" :value="condition.categoryId"
                            ref="refHandle" :options="options" :props="{ checkStrictly: true }" @change="handleChange"
                            clearable></el-cascader>
                    </el-form-item>
                    <el-form-item class="handle-del mr10">
                        <el-input placeholder="输入课程名称" v-model="condition.productName"></el-input>
                    </el-form-item>
                    <el-form-item class="handle-del mr10">
                        <el-date-picker type="date" placeholder="开始日期" v-model="condition.startTime" style="width:200px"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>
                    <el-form-item class="handle-del mr10">
                        <el-date-picker type="date" placeholder="截止日期" v-model="condition.endTime" style="width:200px"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleSearch()"><i class="el-icon-search"></i>搜索</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleAddProduct()">新增课程</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col>
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="categoryName" label="课程类别" width="450px">
                        <template slot-scope="scope">
                            <el-cascader placeholder="选择课程类别:" :value="scope.row.categoryId" ref="refHandle"
                                style="background-color:white; width:70%" :options="options" :disabled="true"
                                :props="{ checkStrictly: true }" @change="handleChange2" clearable></el-cascader>
                        </template>
                    </el-table-column>
                    <el-table-column prop="productName" label="课程名" width="120">
                    </el-table-column>
                    <el-table-column prop="campusName" label="校区" width="120">
                    </el-table-column>
                    <el-table-column prop="startDate" label="开始时间" width="120">
                    </el-table-column>
                    <el-table-column prop="endDate" label="到期时间" width="120">
                    </el-table-column>
                    <el-table-column prop="creator" label="创建人" width="120">
                    </el-table-column>
                    <el-table-column label="操作" width="450" align="center">
                        <template slot-scope="scope">
                            <el-button size="mini" type="primary" @click="courseshow(scope.row)">课节管理</el-button>
                            <el-button v-show="checkCampus(scope.row.campusId)" size="mini" type="primary"
                                @click="handoutShow(scope.row)">讲义管理</el-button>
                            <el-button v-show="checkCampus(scope.row.campusId)" size="mini" type="primary"
                                @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button v-show="checkCampus(scope.row.campusId)" size="mini" type="danger"
                                @click="handleDelet(scope.row)">删除</el-button>
                            <el-button v-show="checkCampus(scope.row.campusId)" size="mini" type="primary"
                                @click="handleCopy(scope.row)">复制课程</el-button>
                            <el-button v-show="checkCampus(scope.row.campusId)" size="mini" type="primary"
                                @click="Daochu(scope.row)">一键导出</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
            <el-col :span="24" class="toolbar">
                <my-page v-if="hackReset" @repeatQry="search()" :pagecondition="condition" :resultData="resultData"
                    :needCount=true></my-page>
            </el-col>
        </div>

        <!-- 编辑弹出框 -->
        <div class="container" v-if="editHtml">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 课程管理</el-breadcrumb-item>
                    <el-breadcrumb-item>编辑</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <div class="form-box">
                <el-form ref="form" :model="temp" label-width="100px">
                    <el-form-item label="课程标题">
                        <el-input v-model="temp.productName"></el-input>
                    </el-form-item>
                    <el-form-item label="课程类别">
                        <el-cascader placeholder="选择课程类别:" :value="temp.categoryId" ref="refHandle" style="width:70%"
                            :options="options" :props="{ checkStrictly: true }" @change="handleChange2"
                            clearable></el-cascader>
                    </el-form-item>
                    <el-form-item label="课程有效期">
                        <el-col :span="11">
                            <el-date-picker type="date" placeholder="开始日期" v-model="temp.startDate" style="width: 100%;"
                                value-format="yyyy-MM-dd" format="yyyy-MM-dd"></el-date-picker>
                        </el-col>
                        <el-col class="line" :span="2">-</el-col>
                        <el-col :span="11">
                            <el-date-picker type="date" placeholder="结束日期" v-model="temp.endDate"
                                value-format="yyyy-MM-dd" format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="课程内容简介">
                        <el-input type="textarea" placeholder="请输入内容" v-model="temp.productDescription" maxlength="100"
                            show-word-limit>
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="updateEdit">确 定</el-button>
                <el-button @click="closeEdit">取 消</el-button>
            </span>
        </div>

        <!-- 新增弹出框 -->
        <div class="container" v-if="insertHtml2">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 课程管理</el-breadcrumb-item>
                    <el-breadcrumb-item>增加</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <div class="form-box">
                <el-form ref="form" :model="temp" label-width="100px" :rules="rules">
                    <el-form-item label="课程标题" prop="productName">
                        <el-input v-model="temp.productName"></el-input>
                    </el-form-item>
                    <el-form-item label="课程类别">
                        <el-cascader placeholder="选择课程类别:" :value="temp.categoryId" ref="refHandle" :options="options2"
                            :props="{ checkStrictly: true }" :show-all-levels="false" @change="handleChange2"
                            popper-class="theder" clearable></el-cascader>
                    </el-form-item>
                    <el-form-item label="课程有效期">
                        <el-col :span="11">
                            <el-date-picker type="date" placeholder="开始日期" v-model="temp.startDate" style="width: 100%;"
                                value-format="yyyy-MM-dd" format="yyyy-MM-dd"></el-date-picker>
                        </el-col>
                        <el-col class="line" :span="2">-</el-col>
                        <el-col :span="11">
                            <el-date-picker type="date" placeholder="结束日期" v-model="temp.endDate"
                                value-format="yyyy-MM-dd" format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="课程内容简介">
                        <el-input type="textarea" placeholder="请输入内容" v-model="temp.productDescription" maxlength="100"
                            show-word-limit>
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="addProduct">确 定</el-button>
                <el-button @click="closeEdit">取 消</el-button>
            </span>
        </div>

        <!--复制课程-->
        <div>
            <el-dialog title="复制课程" :visible.sync="isShowCopyVisible">
                <el-col>
                    <el-form class="demo-form-inline">
                        <el-form-item label="要复制的课程为：">
                            <span>{{ temp.productName }}</span>
                        </el-form-item>
                        <el-form-item label="输入复制后的课程名：">
                            <el-input v-model="temp.newProductName"></el-input>
                        </el-form-item>
                        <el-form-item label="选择复制到的类别:">
                            <el-cascader placeholder="选择课程类别:" :value="temp.newProductCategoryId" ref="refHandle"
                                :options="options" :props="{ checkStrictly: true }" :show-all-levels="false"
                                @change="handleChange3" clearable></el-cascader>
                        </el-form-item>
                    </el-form>
                </el-col>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="isShowCopyVisible = false">取消</el-button>
                    <el-button type="primary" :loading="listLoading" @click="copyProduct()"
                        class="title1">确定</el-button>
                </div>
            </el-dialog>
        </div>
    </el-row>
</template>
<!-- 引入组件库 -->
<script>
export default {
    name: "productindex",
    data() {
        // 自定义校验
        var douhao = (rule, value, callback) => {
            // callback 必须调用
            var reg = /^[^,]+$/gi
            if (!reg.test(value)) {
                callback(new Error('禁止使用英文逗号'));
            } else {
                callback();
            }
        };
        return {
            condition: {},
            resultData: {},
            hackReset: true,
            tableData: [],
            productId: '',
            options: [],
            options2: [],
            temp: {},
            listLoading: false,
            insertHtml1: true,
            insertHtml2: false,
            editHtml: false,
            isShowCopyVisible: false,
            campusList: [],
            rules: {
                productName: [{ required: true, message: "课程标题不能为空", trigger: "blur" },
                { validator: douhao, trigger: 'blur' }],
            },
        }
    },
    created() {
        this.search();
        this.fetchCategory();
        this.fetchCategory3();
    },
    mounted() {
        this.searchCampus();
    },
    methods: {
        checkCampus(value) {
            let isExist = false;
            let buttonpermsStr = sessionStorage.getItem("permCampus");
            if (buttonpermsStr == undefined || buttonpermsStr == null) {
                return false;
            }

            let buttonperms = JSON.parse(buttonpermsStr);

            for (let i = 0; i < buttonperms.length; i++) {
                if (value.indexOf(buttonperms[i].campusId) > -1) {
                    isExist = true;
                    break;
                }
            }
            return isExist;
        },
        handleSearch() {
            this.hackReset = false
            this.$nextTick(() => {
                this.hackReset = true
                this.search();
            })
        },
        handleAddProduct() {
            this.temp = {};
            this.insertHtml1 = false;
            this.insertHtml2 = true;
        },
        handleEdit(row) {
            this.insertHtml1 = false;
            this.editHtml = true;
            this.temp = Object.assign({}, row);
        },
        closeEdit() {
            this.insertHtml2 = false;
            this.editHtml = false;
            this.insertHtml1 = true;
            this.handleSearch();
        },
        handleDelet(row) {
            this.$confirm('此操作将删除, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const tempData = Object.assign({}, row);
                this.url = '/mgr/product/deletProduct';
                this.$axios.post(this.url, tempData)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.$message.success(`删除成功`);
                            this.search();
                        } else {
                            this.$message.error(res.data.errMsg);
                        }
                    })
            });

        },
        handleCopy(row) {
            this.isShowCopyVisible = true;
            this.temp = Object.assign({}, row);
        },
        copyProduct() {
            this.listLoading = true;
            const tempData = Object.assign({}, this.temp);
            tempData.productId = tempData.id
            this.url = '/mgr/product/copyProduct';
            this.$axios.post(this.url, tempData)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.$message.success(`复制成功`);
                        this.listLoading = false;
                        this.isShowCopyVisible = false;
                        this.search();
                    } else {
                        this.listLoading = false;
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        addProduct() {
            const tempData = Object.assign({}, this.temp);
            this.url = '/mgr/product/addProduct';
            this.$axios.post(this.url, tempData)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.$message.success(`新建成功`);
                        this.insertHtml1 = true;
                        this.insertHtml2 = false;
                        this.search();
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        updateEdit() {
            const tempData = Object.assign({}, this.temp);
            this.url = '/mgr/product/editProduct';
            this.$axios.post(this.url, tempData)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.insertHtml1 = true;
                        this.editHtml = false;
                        this.search();
                        this.$message.success(`修改成功`);
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        search() {
            let params = this.condition;
            params.closeCampusPerm = true;
            this.url = '/mgr/product/getProductListAll';
            this.$axios.post(this.url, params)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.resultData = res.data.body;
                        this.tableData = res.data.body.list;
                        console.log(this.tableData);
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        searchCampus() {
            let params = {};
            params.pageSize = 100000;
            this.url = '/mgr/campus/getCampusList';
            this.$axios.post(this.url, params)
                .then((res) => {
                    if (res.data.state === "success") {
                        let thembObject = {
                            id: '',
                            campusId: "",
                            name: '全部校区'
                        }
                        this.campusList = res.data.body.list;
                        this.campusList.unshift(thembObject)
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        handoutShow(row) {
            this.temp = Object.assign({}, row)
            this.$router.push({
                path: '/product/handout',
                query: { productId: this.temp.id, productName: this.temp.productName }
            });

        },
        courseshow(row) {
            this.temp = Object.assign({}, row)
            this.$router.push({
                path: '/product/course',
                query: { productId: this.temp.id, campusId: this.temp.campusId }
            });

        },
        fetchCategory() {
            // let params = {};
            //  params.campusId = "5c4a3d07-33f5-467f-9e14-950a68c62620"
            this.url = '/mgr/prodcutcategory/getCategorys';
            this.$axios.get(this.url, {})
                .then((res) => {
                    if (res.state === "success") {
                        this.options = res.body
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        fetchCategory3() {
            // let params = {};
            //  params.campusId = "5c4a3d07-33f5-467f-9e14-950a68c62620"
            this.url = '/mgr/prodcutcategory/getCampus';
            this.$axios.get(this.url, {})
                .then((res) => {
                    if (res.state === "success") {
                        this.options2 = res.body
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        reset() {
            this.condition = {};
            this.temp = {}
        },
        handleChange(value) {
            // this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            if (value.length == 0) {
                this.condition.categoryId = null
            } else {
                this.condition.categoryId = value[value.length - 1]
            }

        },
        handleChange2(value) {
            // this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            this.temp.categoryId = value[value.length - 1]
        },
        handleChange3(value) {
            this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            this.temp.newProductCategoryId = value[value.length - 1]
        },
        Daochu(row) {
            console.log(row);
            
            this.url = '/app/product/getExcel';
            this.$axios
                .get(this.url, {
           
                        productId: row.id,

                })
                .then((res) => {
                    console.log(res,"111");

                        require.ensure([], () => {
                            const { export_json_to_excel } = require('../../../static/js/Export2Excel');
                            
                            // 准备表头数据（两行）
                            const headers = [
                                ['高联信息内容', '', '', '', '', '掌上考研信息内容', ''],
                                ['所属模块', '课节名称', '视频链接', '更新时间']
                            ];
                            
                            // 准备表格数据行
                            const rows = res.body.map(item => [
                                item.name, 
                                item.courseName, 
                                item.url, 
                                item.time, 
                            ]);
                            
                            // 合并数据
                            const data = [...headers, ...rows];
                            
                            // 使用空表头（因为数据本身已经包含了表头）
                            const tHeader = [];
                            
                            // 导出Excel
                            export_json_to_excel(tHeader, data, row.productName);
                        });
                })
                .catch(() => {
                    this.$message.error('导出失败');
                });
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input.is-disabled .el-input__inner {
    background-color: #ffffff;
    color: #606266;
    border-color: #ffffff;
}

.el-input.is-disabled .el-input__icon {
    display: none;
}

::v-deep .el-cascader-node>.el-radio {
    position: absolute;
    width: 150px;
    height: 100%;
}
</style>
