<template>
    <div class="app-container">
        <el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
            <el-form :inline="true">
                <el-form-item>
                    <el-button type="primary" @click="editNewHandout()">新增</el-button>
                </el-form-item>
            </el-form>
        </el-col>
        <div class="container">
            <el-table
                :data="tableList"
                style="width: 100%">
                <el-table-column
                    prop="handoutName"
                    label="讲义名字"
                    width="180">
                </el-table-column>
                <el-table-column
                    prop="handoutUrl"
                    label="讲义地址">
                </el-table-column>
                <el-table-column label="操作 ">
                    <template slot-scope="scope">
                        <el-button size="small" type="danger" @click="deleteHandout(scope.row)">删除讲义</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <el-dialog title="新增讲义" :visible.sync="isShowaddHandout">
            <el-form label-width="80px" :model="result" ref="dataForm">
                <el-form-item label="讲义名字" prop="cname">
                    <el-input v-model="result.handoutName"></el-input>
                </el-form-item>
                <el-form-item label="上传文件" :label-width="formLabelWidth">
                    <el-upload
                        ref="uploadExcel"
                        :action="UploadUrl()"
                        :limit=limitNum
                        accept=".pdf,.doc,.docx,.mp3"
                        :before-upload="beforeUploadFile"
                        :on-change="fileChange"
                        :on-exceed="exceedFile"
                        :on-success="handleSuccess"
                        :on-error="handleError"
                        :file-list="fileList">
                        <el-button size="small" type="primary">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip">只能上传PDF,DOC,DOCX,MP3文件</div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="isShowaddHandout = false">取消</el-button>
                <el-button type="primary" :loading="listLoading" @click="addHandout" class="title1">确定</el-button>
            </div>
        </el-dialog>

        <!-- 删除弹框 -->
        <el-dialog
            :title=title
            :visible.sync="deleteVisible"
            width="30%">
            <span>{{description}}</span>
            <span slot="footer" class="dialog-footer">
        <el-button @click="deleteVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDelete">确 定</el-button>
      </span>
        </el-dialog>
    </div>
</template>

<script>
    import axios from 'axios'
    import env from '../../api/env'

    export default {
        data() {
            return {
                deleteVisible:false,
                title: "提示",
                description: "是否要删除讲义",
                limitNum: 1,
                tableList: [],
                productId: '',
                formLabelWidth: '80px',
                isShowaddHandout: false,
                handoutUrl: '',
                temp:'',
                form: {
                    file: ''
                },
                fileList: [],
                result: {
                    handoutName: '',
                    type: '',
                    address: '',
                    password: ''
                }
            }
        },
        created() {
            this.fetchData()
        },
        methods: {
            // 文件超出个数限制时的钩子
            exceedFile(files, fileList) {
                this.$notify.warning({
                    title: '警告',
                    message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
                });
            },
            fetchData() {
                this.listLoading = true,
                 this.url = '/mgr/product/getHandloutByProduct';
                this.$axios.get(this.url, {
                    productId: this.$route.query.productId
                }).then((res) => {
                    if (res.state === "success") {
                        this.tableList = res.body
                        this.listLoading = false
                    } else {
                        this.$message.error('获取讲义列表失败');
                    }
                })
            },
            addHandout() {
                this.url = '/mgr/product/addHandloutByProduct';
                this.$axios.post(this.url,
                    {
                        productId: this.$route.query.productId,
                        handoutUrl: this.handoutUrl,
                        handoutName: this.result.handoutName
                    })
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.listLoading = false
                            this.isShowaddHandout = false
                            this.fetchData()
                        } else {
                            this.$message.error('上传失败');
                            this.listLoading = false
                            this.isShowaddHandout = false
                        }
                    })
            },
            deleteHandout(row){
                this.temp = Object.assign({}, row)
                this.deleteVisible = true
            }
            ,submitDelete(){
                this.url = '/mgr/product/deletHandloutByProduct';
                this.$axios.post(this.url, {
                    id: this.temp.id
                }).then((res) => {
                    if (res.data.state === "success") {
                        //this.tableList = res.data.body
                        this.fetchData()
                        this.deleteVisible = false
                        this.listLoading = false
                        this.$message.success('删除讲义成功');
                    } else {
                        this.$message.error('删除讲义失败');
                    }
                })
            },
            UploadUrl() {
                return env.baseURL + "/common/file/uploadpdf/absolute";
            },
            // 文件状态改变时的钩子
            fileChange(file, fileList) {
                this.form.file = file.raw
            },
            editNewHandout() {
                this.result.type='';
                this.result.handoutName='';
                this.fileList=[]
                this.isShowaddHandout = true
            },

            // 上传文件之前的钩子, 参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
            beforeUploadFile(file) {
                // const isJPG = file.type === 'application/pdf' || file.type === 'application/PDF';
                const isLt2M = file.size / 1024 / 1024 < 2000;

                // if (!isJPG) {
                //     this.$message.error('上传讲义只能是 指定 格式!');
                // }
                if (!isLt2M) {
                    this.$message.error('上传讲义大小不能超过 2000M!');
                }
                return isLt2M;
            },
            // 文件上传成功时的钩子
            handleSuccess(res, file, fileList) {
                this.$notify.success({
                    title: '成功',
                    message: `文件上传成功`
                });
                console.log(res);
                this.handoutUrl = res.body;
            },
            // 文件上传失败时的钩子
            handleError(err, file, fileList) {
                this.$notify.error({
                    title: '错误',
                    message: `文件上传失败`
                });
            },
            uploadFile() {
                this.$refs.uploadExcel.submit()

            }
        }
    }
</script>
