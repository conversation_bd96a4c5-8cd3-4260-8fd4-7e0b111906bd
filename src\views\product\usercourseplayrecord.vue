<!--
 * @Author: <PERSON>
 * @Date: 2022-09-13 10:28:58
 * @LastEditTime: 2022-11-01 10:10:07
 * @Description: 如果没能一次成功，那就叫它1.0版吧。
 * 
 * Copyright (c) by <PERSON>, All Rights Reserved. 
-->
<template>
    <el-row>
        <div v-if="insertHtml1">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i>课节观看统计</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col >
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="courseName" label="课节名" width="120">
                    </el-table-column>
                    <el-table-column prop="playLength" label="时长（秒）" width="120">
                    </el-table-column>
                    <el-table-column prop="playBeginTime" label="首次观看时间" width="120">
                    </el-table-column>
                    <el-table-column prop="playEndTime" label="最后观看时间" width="120">
                    </el-table-column>
                </el-table>
            </el-col>
        </div>
    </el-row>
</template>
<!-- 引入组件库 -->
<script >
    export default {
        name: "usercourseplayrecordindex",
        data() {
            return {
                condition:{},
                hackReset:true,
                tableData: [],
                productId: '',
                sxtime: 0,
                options: [],
                temp:{},
                listLoading: false,
                insertHtml1:true,
                insertHtml2:false,
                editHtml:false,
                isShowCopyVisible:false,
            }
        },
        created() {
            this.productId = this.$route.query.productId;
            this.userId = this.$route.query.userId;
            this.search();
        },
        activated(){
            this.productId = this.$route.query.productId;
            this.userId = this.$route.query.userId;
            this.search();
        },
        methods: {
            handleSearch() {
                this.search();
            },
            handleEdit(row){
                this.insertHtml1=false;
                this.editHtml=true;
                this.temp = Object.assign({}, row);
                this.sxtime=this.temp.seeTimes-this.temp.studyTimes
            },
            closeEdit(){
                this.insertHtml2 = false;
                this.editHtml = false;
                this.insertHtml1=true;
                this.handleSearch();
            },
            search(){
                let params = this.condition;
                this.url = '/mgr/productuser/getStaticsUserPlayRecord';
                this.$axios.get(this.url,{
                    "productId":this.productId,"userId":this.userId
                })
                    .then((res) => {
                        if (res.state === "success") {
                            this.tableData=res.body;
                        } else {
                            this.$message.error(res.errMsg);
                        }
                    })
            },
        }
    }
</script>
