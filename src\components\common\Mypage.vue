<template>
    <div v-if="hackReset">
        <el-pagination background style="float:right;"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page="pageNum"
                       :page-sizes="pageNums"
                       :page-size="pageSize"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="resultData.total">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: "Mypage",
        data() {
            return {
                data:"",
                pageNums:[10, 20, 50,100,500,1000],
                pageSize:10,
                pageNum:1,
                hackReset:true
            }
        },

        props: {
            pagecondition: Object,
            resultData:Object,
            needCount:Boolean,
        },
        mounted(){
            this.pagecondition.pageSize=this.pageSize;
            this.pagecondition.pageNum=this.pageNum;
            this.pagecondition.needCount=this.needCount;
        },
        updated(){
            // this.pagecondition.pageSize=this.pageSize;
            // this.pagecondition.pageNum=this.pageNum;
        },
        methods:{
            handleSizeChange(val) {
                this.pagecondition.pageSize=val;
                this.$emit('repeatQry');

            },
            handleCurrentChange(val) {
                this.pagecondition.pageNum=val;
                this.$emit('repeatQry');

            }
        }
    }
</script>

<style scoped>

</style>
