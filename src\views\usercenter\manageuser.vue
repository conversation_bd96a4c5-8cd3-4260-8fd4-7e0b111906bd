<template>
    <div class="table">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-cascades"></i> 控制台用户管理</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <div class="handle-box">
                <el-button type="primary" icon="search" @click="handleAddUser">新增</el-button>
            </div>
            <el-table :data="tableData" border class="table">
                <el-table-column type="index" width="100" label="序号">
                </el-table-column>
                <el-table-column prop="loginName" label="账户名" width="120">
                </el-table-column>
                <el-table-column prop="nickName" label="姓名" width="120">
                </el-table-column>
                <el-table-column prop="state" label="状态" width="200">
                    <template slot-scope="scope">
                        {{ scope.row.state === 1 ?  '开启': '' }}
                        {{ scope.row.state === 2 ?  '禁用': '' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="400" align="center">
                    <template slot-scope="scope">
                        <el-button v-if="scope.row.state===2" size="mini" type="success"
                                   @click="handlerChangeStatus(scope.row,1)">启用
                        </el-button>
                        <el-button v-if="scope.row.state===1" size="mini" type="danger"
                                   @click="handlerChangeStatus(scope.row,2)">停用
                        </el-button>
                        <el-button size="small" type="primary" @click="setUserRole(scope.row.id)">配置角色</el-button>
                        <el-button size="small" type="primary" @click="handleResetPwd(scope.row)">重置密码</el-button>
                        <el-button size="small" type="primary" @click="handleEditUser(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>

        </div>

        <el-dialog title="配置角色" :visible.sync="setUserRoleVisible">
            <el-transfer v-model="userRoles" :data="roleDatas" @change="handleChange"></el-transfer>
        </el-dialog>

        <el-dialog title="新增用户" :visible.sync="isAddUserVisible">
            <el-form ref="form" :model="formuser" label-width="100px">
                <el-form-item label="用户名">
                    <el-input v-model="formuser.loginName"></el-input>
                </el-form-item>
                <el-form-item label="昵称">
                    <el-input v-model="formuser.nickName"></el-input>
                </el-form-item>
                <el-form-item label="密码">
                    <el-input v-model="formuser.password"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isAddUserVisible = false">取 消</el-button>
                <el-button type="primary" @click="addUser">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="编辑用户" :visible.sync="isEditUserVisible">
            <el-form ref="form" :model="formuser" label-width="100px">
                <el-form-item label="用户名">
                    <el-input v-model="formuser.loginName"></el-input>
                </el-form-item>
                <el-form-item label="昵称">
                    <el-input v-model="formuser.nickName"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isEditUserVisible = false">取 消</el-button>
                <el-button type="primary" @click="editUser">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="重置密码" :visible.sync="resetpwdVisible">
            <el-form ref="form" :model="formuser2"  label-width="100px">
                <el-form-item label="新密码">
                    <el-input v-model="formuser2.password"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="resetpwdVisible = false">取 消</el-button>
                <el-button type="primary" @click="resetPwd">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'basetable',
        data() {
            return {
                condition:{
                    loginName:""
                },
                resultData:{},
                formuser:{},
                formuser2:{},
                hackReset:true,
                setUserRoleVisible:false,
                isAddUserVisible:false,
                isEditUserVisible:false,
                resetpwdVisible:false,
                total:'',
                xyUserId:'',
                tableData: [],
                userRoles:[],
                roleDatas:[],
            }
        },
        created() {
            this.search();
        },
        methods: {
            handleSearch() {
                this.search();
            },
            handleAddUser(){
                this.isAddUserVisible=true;
            },
            handleEditUser(row){
                this.isEditUserVisible=true;
                this.formuser = Object.assign({}, row)
            },
            handleResetPwd(row){
                this.resetpwdVisible=true;
                this.formuser2 = Object.assign({}, row)

            },
            setUserRole(userId){
                this.setUserRoleVisible = true;
                this.xyUserId=userId;
                this.roleDatas=[];
                this.userRoles=[];
                //获取用户角色
                let url = '/mgr/role/getUserRoleByUserId';
                this.$axios.get(url,{
                    userId:userId
                })
                    .then((res) => {
                        if (res.state === "success") {
                            let rets = res.body
                            for (let i = 0; i < rets.length; i++) {
                                if(rets[i].name==='individual'){
                                    continue;
                                }
                                this.roleDatas.push({
                                    key: rets[i].id,
                                    label: rets[i].description,
                                    disabled: false,
                                });
                                if(rets[i].isBind==2){
                                    this.userRoles.push( rets[i].id)
                                }
                            }
                        }else {
                            this.$message.error('查询失败');

                        }
                    })

            },
            search() {
                let params = this.condition;

                this.url = '/mgr/sysuser/getConsoleManagementUsers';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        this.resultData=res.data.body;
                        if (res.data.state === "success") {
                            this.tableData = res.data.body
                        }else {
                            this.$message.error('查询失败');

                        }
                    })
            },
            handlerChangeStatus(row,state){
                this.url = '/mgr/sysuser/changeUserStatus';
                this.temp = Object.assign({}, row)
                this.$axios.post(this.url,
                    {id: this.temp.id,state:state})
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.search()
                        } else {
                            this.$message.error('操作失败');
                        }
                    })
            },
            //添加用户
            addUser(){
                let temp = Object.assign({}, this.formuser)
                let url = '/mgr/sysuser/addUser';
                this.$axios.post(url,temp)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.search()
                            this.formuser={};
                            this.isAddUserVisible=false;
                        }else {
                            this.$message.error('新增失败');
                        }
                    })
            },
            //编辑用户
            editUser(){
                let temp = Object.assign({}, this.formuser)
                temp.userId=temp.id
                let url = '/mgr/sysuser/editUser';
                this.$axios.post(url,temp)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.search()
                            this.formuser={};
                            this.isEditUserVisible=false;
                        }else {
                            this.$message.error('新增失败');
                        }
                    })
            },
            //重置密码
            resetPwd(){
                let temp = Object.assign({}, this.formuser2)
                let param={}
                param.id=temp.id
                param.password=temp.password
                let url = '/mgr/sysuser/resetPwd';
                this.$axios.post(url,param)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.search()
                            this.formuser={};
                            this.resetpwdVisible=false;
                        }else {
                            this.$message.error('失败');
                        }
                    })
            },
            handleChange(value, direction, movedKeys) {
                console.log(this.roleDatas);
                console.log(value, direction, movedKeys);
                let userId=this.xyUserId;
                let roleIds=movedKeys;
                let url="";
                if("left"==direction){ //删除
                     url = '/mgr/role/deleteUserRole';
                }else if("right"==direction){ //添加
                    url = '/mgr/role/addUserRole';
                }

                this.$axios.post(url,
                    {userId: userId,roleIds:roleIds}
                    )
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.$message.success("修改成功");
                        } else {
                            this.$message.error('操作失败');
                        }
                    })
            },
    }}
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }
    .table{
        width: 100%;
        font-size: 14px;
    }
    .input_span span {
        display: inline-block;
        width: 85px;
        height: 10px;
        background: #eee;
        line-height: 20px;
    }
</style>
