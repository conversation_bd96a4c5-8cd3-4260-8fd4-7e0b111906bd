<template>
  <el-row class="container">
    <h3 class="tab-title">课程计划</h3>
    <router-view></router-view>
    <el-row class="task-list">
      <el-button v-show="checkCampus(campusId)" type="text" icon="el-icon-plus" class="task-right task-cursor"
        @click="handleAddCategory()">添加章</el-button>
      <draggable disabled='true' style="padding-left: 20px;padding-top: 10px;" v-model='tableData' element='el-timeline'>
        <el-row v-for="(item, index) in tableData" :key="item.courseCategoryId" style="margin-top: 20px;"
          :id="item.courseCategoryId">
          <span class="task-left list-group-item">第{{ index + 1 }}课：{{ item.categoryName }}</span>
          <el-button v-show="checkCampus(campusId)" type="text" icon="el-icon-plus" class="task-right task-cursor"
            @click="handleAddCourse(item.courseCategoryId)">添加课节</el-button>
          <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor"
            @click="handleEditLesson(item)">编 辑</el-button>
          <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor" :loading="listLoading"
            @click="delLesson(item.courseCategoryId)">删 除</el-button>
          <div class="clearfix"></div>
          <draggable style="padding-left: 20px;padding-top: 30px" v-model='item.courseChildren' disabled='true'
            element='el-timeline'>
            <el-timeline-item class="list-group-item" :parentid="course.courseUnitId" :id="course.id"
              v-for="(course, index) in item.courseChildren" :key="index" :hide-timestamp="true"
              :timestamp="course.createTime">
              <div style="display: flex;justify-content: space-between;align-items: center;">
                <div>
                  第{{ index + 1 }}节：{{ course.courseName }} <br />
                  上课时间：{{ dateFormat("YYYY-mm-dd HH:MM", new Date(course.startTime)) }}-{{ dateFormat("HH:MM", new
                    Date(course.endTime)) }}
                  <br />
                  手动短信发送时间：{{ course.msgTime }}
                </div>
                <!--<span  ></span>-->
                <div>
                  <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor">自动短信:
                    <el-switch v-model="course.isAutoMsg" active-value="1" inactive-value="2"
                      @change="changeAuto(course.id, course.isAutoMsg)" active-color="#13ce66" inactive-color="#ff4949">
                    </el-switch>

                  </el-button>
                  <el-button type="text" class="task-right task-cursor">是否点播播放:
                    <el-switch v-model="course.liveVideoType" active-value="1" inactive-value="2"
                      @change="changeLiveVideoType(course.id, course.liveVideoType)" active-color="#13ce66"
                      inactive-color="#ff4949">
                    </el-switch>

                  </el-button>
                  <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor"
                    @click="handleSetMsgTime(course)">
                    设置短信发送时间
                  </el-button>
                  <el-button v-show="checkCampus(campusId)" v-has="'course:download'" type="text"
                    class="task-right task-cursor" @click="dowloadVideo(course.id)">下 载</el-button>
                    <el-button v-show="checkCampus(campusId)" v-has="'course:download'" type="text"
                    class="task-right task-cursor" @click="copyDownloadLink(course.id)">复制下载链接</el-button>
                  <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor"
                    @click="delCourse(course.id)">删 除</el-button>
                  <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor"
                    @click="handleEditCourse(course)">编 辑</el-button>
                  <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor"
                    @click="handleCopy(course)">复制课节</el-button>
                  <el-button v-show="checkCampus(campusId)" type="text" class="task-right task-cursor"
                    @click="sortSeq(course)">排序</el-button>

                  <a v-show="checkCampus(campusId)" v-if="course.courseType == 1" class="task-right task-cursor"
                    @click="uploadVideo(course)">
                    <el-button v-if="course.isBroadcast == '1'">已上传</el-button>
                    <el-button v-if="course.isBroadcast == '2' && (course.videoId == null || course.videoId == '')">待上传</el-button>
                    <el-button v-if="course.isBroadcast == '2' && course.videoId != null && course.videoId != ''">视频转码中</el-button>
                  </a>
                  <a v-show="checkCampus(campusId)" v-if="course.courseType == 2" class="task-right task-cursor">
                    <el-button v-if="course.liveroomId == '' || course.liveroomId == null" @click="buildRoom(course)">建立直播间</el-button>
                    <el-button v-if="course.liveroomId == '' || course.liveroomId == null" v-has="'create:liveroom'"  @click="buildexternalRoom(course)">外部建立</el-button>
                    <el-button v-if="course.liveroomId != '' && course.liveroomId != null"
                      @click="handleEnterLiveRoom(course)">去直播</el-button>
                    <el-button v-if="course.liveroomId != '' && course.liveroomId != null" size="mini"
                      @click="buildRoom(course)">重建直播间
                    </el-button>
                    <el-button v-if="course.liveroomId != '' && course.liveroomId != null" size="mini"
                      @click="playBack(course.liveroomId)">回放统计
                    </el-button>
                  </a>
                </div>
              </div>
            </el-timeline-item>
          </draggable>
        </el-row>
      </draggable>
    </el-row>

    <!--添加章节-->
    <div>
      <el-dialog title="添加章" :visible.sync="addllessonVisible">
        <el-col>
          <el-form class="demo-form-inline">
            <el-form-item label="章节名称：">
              <el-input v-model="temp.categoryName"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addllessonVisible = false">取消</el-button>
          <el-button type="primary" :loading="listLoading" @click="addLesson()" class="title1">确定</el-button>
        </div>
      </el-dialog>
    </div>
    <!--编辑章节-->
    <div>
      <el-dialog title="编辑章节" :visible.sync="editlessonVisible">
        <el-col>
          <el-form class="demo-form-inline">
            <el-form-item label="章节名称：">
              <el-input v-model="temp.categoryName"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editlessonVisible = false">取消</el-button>
          <el-button type="primary" :loading="listLoading" @click="editLesson()" class="title1">确定</el-button>
        </div>
      </el-dialog>
    </div>

    <!--添加课节-->
    <div>
      <el-dialog title="添加课节" :visible.sync="addCourseVisible">
        <el-col>
          <el-form class="demo-form-inline" label-width="100px">
            <el-form-item label="课节名：">
              <el-col :span="12">
                <el-input v-model="temp.courseName"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="课节类型：">
              <template>
                <el-radio-group v-model="temp.courseType">
                  <el-radio label="1">点播课</el-radio>
                  <el-radio label="2">直播课</el-radio>
                </el-radio-group>
              </template>
            </el-form-item>
            <el-form-item label="上课时间：">
              <el-col :span="6">
                <el-date-picker type="datetime" placeholder="开始时间" v-model="temp.startTime" style="width: 100%;"
                  value-format="yyyy-MM-dd HH:mm:SS" format="yyyy-MM-dd HH:mm:SS"></el-date-picker>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="6">
                <el-date-picker type="datetime" placeholder="结束时间" v-model="temp.endTime"
                  value-format="yyyy-MM-dd HH:mm:SS" format="yyyy-MM-dd HH:mm:SS" style="width: 100%;"></el-date-picker>
              </el-col>
            </el-form-item>

            <el-form-item label="VIP教师：">
              <el-select v-model="temp.teacherId" filterable placeholder="请选择VIP教师">
                <el-option
                  v-for="item in vipTeacher"
                  :key="item.teacherId"
                  :label="item.teacherName"
                  :value="item.teacherId"
           
                ></el-option>
              </el-select>
            </el-form-item>

          </el-form>
        </el-col>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addCourseVisible = false">取消</el-button>
          <el-button type="primary" :loading="listLoading" @click="addCourse()" class="title1">确定</el-button>
        </div>
      </el-dialog>
    </div>

    <!--编辑课节-->
    <div>
      <el-dialog title="编辑课节" :visible.sync="editCourseVisible">
        <el-col>
          <el-form class="demo-form-inline" label-width="100px">
            <el-form-item label="课节名：">
              <el-col :span="12">
                <el-input v-model="temp.courseName"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="课节类型：">
              <template>
                <el-radio-group v-model="temp.courseType">
                  <el-radio disabled label="1">点播课</el-radio>
                  <el-radio disabled label="2">直播课</el-radio>
                </el-radio-group>
              </template>
            </el-form-item>
            <el-form-item label="上课时间：">
              <el-col :span="6">
                <el-date-picker type="datetime" placeholder="开始时间" v-model="temp.startTime" style="width: 100%;"
                  value-format="yyyy-MM-dd HH:mm:SS" format="yyyy-MM-dd HH:mm:SS"></el-date-picker>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="6">
                <el-date-picker type="datetime" placeholder="结束时间" v-model="temp.endTime"
                  value-format="yyyy-MM-dd HH:mm:SS" format="yyyy-MM-dd HH:mm:SS" style="width: 100%;"></el-date-picker>
              </el-col>
            </el-form-item>

     
            <el-form-item label="VIP教师：">
              <el-select v-model="temp.teacherId" filterable placeholder="请选择VIP教师">
                <el-option
                  v-for="item in vipTeacher"
                  :key="item.teacherId"
                  :label="item.teacherName"
                  :value="item.teacherId"
           
                ></el-option>
              </el-select>
            </el-form-item>
       

            
          </el-form>
        </el-col>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editCourseVisible = false">取消</el-button>
          <el-button type="primary" :loading="listLoading" @click="editCourse()" class="title1">确定</el-button>
        </div>
      </el-dialog>
    </div>
    <div>
      <el-dialog title="身份" :visible.sync="dialogVisible" width="30%">
        <span>请选择您的身份</span>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="golive(1)">讲师</el-button>
          <el-button type="primary" @click="golive(2)">助教</el-button>
        </span>
        <br /><br /><br />
        <div><span>讲师参加码：{{ this.liveroomInfo.teacher_code }}</span></div>
        <br />
        <div><span>助教参加码：{{ this.liveroomInfo.admin_code }}</span></div>
        <p>老师：</p>
        <p>链接：</p><span>{{ teacherurl }}</span>
        <!-- <p>密码：</p><span>{{teachercode }}</span> -->
        <p>------------------------------------</p>
        <p>助教：</p>
        <p>链接：</p><span>{{ zhujiaourl }}</span>
        <!-- <p>密码：</p><span>{{ zhujiaocode }}</span> -->
        <p>------------------------------------</p>
        <p>学生观看链接：</p><span>{{ bookurl }}&autoLogin=true&viewername=glwangxiao</span>
        <p>------------------------------------</p>
        <p>推流地址：</p>
        <div v-for="(v, i) in tuiliuurl" :key="i">
          <p>链接：</p><span>{{ v }}</span>
        </div>
        <p>------------------------------------</p>
        <p>第三方推流(主持人)</p>

        <p>链接：</p><span>{{ hosturl }}</span>
        <p>------------------------------------</p>
        <p>拉流地址</p>
        <p>链接：</p>
        <p v-for="(x,c) in pullurl" :key="c">{{ x }}</p>
      </el-dialog>
    </div>
    <div>
      <el-dialog title="复制课节" :visible.sync="isShowCopyVisible">
        <el-col>
          <el-form class="demo-form-inline">
            <el-form-item class="handle-del mr10">
              <el-cascader placeholder="选择课程类别:" :value="categoryId" ref="refHandle" :options="options"
                :props="{ checkStrictly: true }" @change="handleChange" clearable></el-cascader>
            </el-form-item>
            <el-form-item label="复制课程名：">
              <el-select v-model="copyProductId" placeholder="请选择" class="handle-select mr10">
                <el-option v-for="item in productList" :key="item.id" :label="item.productName" :value=item.id>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="第几个课节目录下：">
              <el-input type="number" v-model="cateindex" placeholder="请输入内容" width="50px"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <div slot="footer" class="dialog-footer">
          <el-button @click="isShowCopyVisible = false">取消</el-button>
          <el-button type="primary" :loading="listLoading" @click="copyProductCourse()" class="title1">确定</el-button>
        </div>
      </el-dialog>
    </div>

    <!--排序-->
    <div>
      <el-dialog :visible.sync="isSortVisible">
        <el-form ref="temp" :model="temp" title="顺序" label-width="200px">
          <el-form-item label="顺序">
            <el-input v-model="temp.seq"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSort">确定</el-button>
            <el-button @click="isSortVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
    <!--设置发送短信时间-->
    <div>
      <el-dialog :visible.sync="isSetMsgVisible">
        <el-radio-group v-model="radio" @change="changeTimeType">
          <el-radio :label="1">立即发送</el-radio>
          <el-radio :label="2">设置时间</el-radio>
        </el-radio-group>
        <el-form ref="temp" :model="temp">
          <el-form-item v-show="showMsgTime" label="发送时间">
            <el-date-picker type="datetime" placeholder="发送时间" v-model="msgTime" style="width:250px"
              value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="setMsgTime">确定</el-button>
            <el-button @click="isSetMsgVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </el-row>
</template>

<script >
import draggable from 'vuedraggable';
export default {
  name: 'productcourse',
  data() {
    return {
      tableData: [],
      addllessonVisible: false,
      editlessonVisible: false,
      addCourseVisible: false,
      editCourseVisible: false,
      dialogVisible: false,
      isShowCopyVisible: false,
      broadcast: 1,
      cateindex: 1,
      temp: {},
      liveroomId: null,
      liveroomInfo: {},
      categoryId: '',
      copyProductId: '',
      listLoading: false,
      productId: this.$route.query.productId,
      options: [],
      productList: [],
      isSortVisible: false,
      isSetMsgVisible: false,
      showMsgTime: false,
      msgTime: '',
      radio: 1,
      campusId: this.$route.query.campusId,
      teacherurl: '',
      zhujiaourl: '',
      bookurl: '',
      teachercode: '',
      zhujiaocode: '',
      tuiliuurl: [],
      hosturl: '',
      pullurl:'',
      vipTeacher: [],
    };
  },
  components: {
    draggable,
  },
  mounted() {
    this.search();
  },
  created() {
    this.search();
    this.searchTeacher();
  },
  watch: {
    $route(to, from) {

      // console.log(this.productId);

      // this.$router.go(0);
      // window.location.reload();
    },
  },
  activated() {
    this.productId = this.$route.query.productId
    this.search();
  },
  methods: {
    checkCampus(value) {
      let isExist = false;
      let buttonpermsStr = sessionStorage.getItem('permCampus');
      if (buttonpermsStr == undefined || buttonpermsStr == null) {
        return false;
      }

      let buttonperms = JSON.parse(buttonpermsStr);

      for (let i = 0; i < buttonperms.length; i++) {
        // console.log(value, '==========================================');
        if (value.indexOf(buttonperms[i].campusId) > -1) {
          isExist = true;
          break;
        }
      }
      return isExist;
    },
    changeTimeType(data) {
      if (data == 1) {
        this.isCurrent = '1';
        this.msgTime = '';
        this.showMsgTime = false;
      } else if (data == 2) {
        this.isCurrent = '2';
        this.showMsgTime = true;
      }
    },

    handleSetMsgTime(row) {
      this.isSetMsgVisible = true;
      this.msgTime = '';
      this.isCurrent = '1';
      this.radio = 1;
      this.showMsgTime = false;
      this.temp = Object.assign({}, row);
    },

    setMsgTime() {
      this.url = '/mgr/productcourse/setMsgTime';
      this.$axios
        .get(this.url, {
          courseId: this.temp.id,
          msgTime: this.msgTime,
          isCurrent: this.isCurrent,
        })
        .then((res) => {
          if (res.state === 'success') {
            this.$message.success('成功');
            this.isSetMsgVisible = false;
            this.search();
          } else {
            this.$message.error('失败');
          }
        });
    },
    changeAuto(courseId, isAutoMsg) {
      this.url = '/mgr/productcourse/swichAutoMsg';
      this.$axios
        .get(this.url, {
          courseId: courseId,
          isAutoMsg: isAutoMsg,
        })
        .then((res) => {
          if (res.state === 'success') {
            this.search();
          } else {
            this.$message.error('失败');
          }
        });
    },
    changeLiveVideoType(courseId, liveVideoType) {
      this.url = '/mgr/productcourse/swichLiveVideoType';
      this.$axios
        .get(this.url, {
          courseId: courseId,
          liveVideoType: liveVideoType,
        })
        .then((res) => {
          if (res.state === 'success') {
            this.search();
          } else {
            this.$message.error('失败');
          }
        });
    },
    handleEditLesson(row) {
      this.editlessonVisible = true;
      this.temp = Object.assign({}, row);
    },
    handleAddCourse(courseCategoryId) {
      this.addCourseVisible = true;
      this.temp = {};
      this.temp.courseCategoryId = courseCategoryId;
    },
    handleEditCourse(row) {
      this.editCourseVisible = true;
      this.temp = Object.assign({}, row);
      this.temp.courseType = this.temp.courseType;
    },
    handleAddCategory() {
      this.addllessonVisible = true;
      this.temp = {};
    },
    handleCopy(row) {
      this.isShowCopyVisible = true;
      this.temp = Object.assign({}, row);
      this.fetchCategory();
    },
    handleChange(value) {
      // this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
      if (value.length == 0) {
        this.categoryId = null;
      } else {
        this.categoryId = value[value.length - 1];
      }

      this.searchProduct();
    },
    search() {
      this.url = '/mgr/productcourse/getProductCourseByProduct';
      this.$axios
        .get(this.url, {
          productId: this.productId,
        })
        .then((res) => {
          if (res.state === 'success') {
            this.tableData = res.body;
          } else {
            this.$message.error('获取失败');
          }
        });
    },

    searchTeacher() {
      this.url = '/mgr/teacher/query';
      this.$axios
        .get(this.url, {})
        .then((res) => {
          this.vipTeacher = res;
        });
    },

    //排序
    sortSeq(row) {
      this.isSortVisible = true;
      this.temp = Object.assign({}, row);
    },
    onSort() {
      this.url = '/mgr/productcourse/editProductCourse';
      this.$axios.post(this.url, this.temp).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success('更改成功');
          this.isSortVisible = false;
          this.search();
        } else {
          this.$message.error('更改排序失败');
        }
      });
    },
    //添加章节
    addLesson() {
      this.listLoading = true;
      const tempData = Object.assign({}, this.temp);
      tempData.productId = this.productId;
      this.url = '/mgr/productcourse/addProductCourseCategory';
      this.$axios.post(this.url, tempData).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success(`成功`);
          this.listLoading = false;
          this.addllessonVisible = false;
          this.search();
        } else {
          this.listLoading = false;
          this.$message.error(res.data.errMsg);
        }
      });
    },
    //编辑章节
    editLesson() {
      this.listLoading = true;
      const tempData = Object.assign({}, this.temp);
      let parm = {};
      parm.id = tempData.courseCategoryId;
      parm.categoryName = tempData.categoryName;
      this.url = '/mgr/productcourse/editProductCourseCategory';
      this.$axios.post(this.url, parm).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success(`成功`);
          this.listLoading = false;
          this.editlessonVisible = false;
          this.search();
        } else {
          this.listLoading = false;
          this.$message.error(res.data.errMsg);
        }
      });
    },
    //删除章节
    delLesson(courseCategoryId) {
      this.listLoading = true;
      this.url = '/mgr/productcourse/deleteProductCourseCategory';
      this.$axios
        .post(this.url, {
          id: courseCategoryId,
        })
        .then((res) => {
          if (res.data.state === 'success') {
            this.$message.success(`删除成功`);
            this.listLoading = false;
            this.search();
          } else {
            this.listLoading = false;
            this.$message.error(res.data.errMsg);
          }
        });
    },
    //添加课节
    addCourse() {
      this.listLoading = true;
      const tempData = Object.assign({}, this.temp);
      tempData.productId = this.productId;
      this.url = '/mgr/productcourse/addProductCourse';
      this.$axios.post(this.url, tempData).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success(`成功`);
          this.listLoading = false;
          this.addCourseVisible = false;
          this.search();
        } else {
          this.listLoading = false;
          this.$message.error(res.data.errMsg);
        }
      });
    },
    //删除课节
    delCourse(courseId) {
      this.$confirm('此操作将删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.listLoading = true;
        this.url = '/mgr/productcourse/deletProductCourse';
        this.$axios
          .post(this.url, {
            id: courseId,
          })
          .then((res) => {
            if (res.data.state === 'success') {
              this.$message.success(`删除成功`);
              this.listLoading = false;
              this.search();
            } else {
              this.listLoading = false;
              this.$message.error(res.data.errMsg);
            }
          });
      });
    },
    dowloadVideo(courseId) {
      this.url = '/mgr/productcourse/getCourseVideoUrl';
      this.$axios
        .post(this.url, {
          id: courseId,
        })
        .then((res) => {
          if (res.data.state === 'success') {
            let link = document.createElement('a'); // 创建a标签
            link.setAttribute('target', '_blank');
            link.style.display = 'none';
            link.href = res.data.body; // 设置下载地址
            link.setAttribute('download', ''); // 添加downLoad属性
            document.body.appendChild(link);
            link.click();
          } else {
            this.$message.error(res.data.errMsg);
          }
        });
    },
    copyDownloadLink(courseId) {
      this.url = '/mgr/productcourse/getCourseVideoUrl';
      this.$axios
        .post(this.url, {
          id: courseId,
        })
        .then((res) => {
          if (res.data.state === 'success') {
            // 创建一个临时input元素用于复制
            let input = document.createElement('input');
            document.body.appendChild(input);
            input.value = res.data.body;
            input.select();
            document.execCommand('copy');
            document.body.removeChild(input);
            this.$message.success('下载链接已复制到剪贴板');
          } else {
            this.$message.error(res.data.errMsg);
          }
        });
    },
    //编辑课节
    editCourse() {
      this.listLoading = true;
      const tempData = Object.assign({}, this.temp);
      this.url = '/mgr/productcourse/editProductCourse';
      this.$axios.post(this.url, tempData).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success(`成功`);
          this.listLoading = false;
          this.editCourseVisible = false;
          this.search();
        } else {
          this.listLoading = false;
          this.$message.error(res.data.errMsg);
        }
      });
    },
    //复制课节目录
    copyProductCourse() {
      this.listLoading = true;
      let parm = {};
      parm.productId = this.copyProductId;
      parm.id = this.temp.id;
      parm.cateindex = this.cateindex;
      this.url = '/mgr/productcourse/copyProductCourse';
      this.$axios.post(this.url, parm).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success(`复制成功`);
          this.listLoading = false;
          this.isShowCopyVisible = false;
          this.search();
        } else {
          this.listLoading = false;
          this.$message.error(res.data.errMsg);
        }
      });
    },
    //获取课城目录
    fetchCategory() {
      this.url = '/mgr/prodcutcategory/getCategorys';
      this.$axios.post(this.url, {}).then((res) => {
        if (res.data.state === 'success') {
          this.options = res.data.body;
        } else {
          this.$message.error(res.data.errMsg);
        }
      });
    },
    //根据课程目录获取课程
    searchProduct() {
      let params = {};
      params.categoryId = this.categoryId;
      params.pageSize = 10000;
      this.url = '/mgr/product/getProductList';
      this.$axios.post(this.url, params).then((res) => {
        if (res.data.state === 'success') {
          // this.resultData=res.data.body;
          this.productList = res.data.body.list;
        } else {
          this.$message.error(res.data.errMsg);
        }
      });
    },
    uploadVideo(course) {
      this.$confirm('此操作将会替换已上传过的视频, 是否继续?', '上传视频', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$router.push({
          path: '/byj/videoupload',
          query: { courseId: course.id },
        });
      });
    },
    //建立直播间
    buildRoom(course) {
      this.$router.push({
        path: '/addBjyLiveRoom',
        query: { courseId: course.id, productId: this.productId, campusId: this.campusId },
      });
    },
    //建立外部直播间
    buildexternalRoom(course) {
      this.$router.push({
        path: '/externalAddLiveRoom',
        query: { courseId: course.id, productId: this.productId, campusId: this.campusId },
      });
    },
    handleEnterLiveRoom(course) {

      this.liveroomId = course.liveroomId;
      this.enterLive(1)
      this.pull(course.liveroomId)
      this.url = '/manage/bjylive/getLiveRoomById';
      this.$axios
        .get(this.url, {
          liveroomId: course.liveroomId,
        })
        .then((res) => {
          if (res.state === 'success') {
            this.liveroomInfo = res.body;
            this.dialogVisible = true;
          } else {
            this.listLoading = false;
            this.$message.error(res.data.errMsg);
          }
        });
    },
    golive(num) {
      if (num == 1) {
        window.location.href = this.teacherurl;
      } else {
        window.location.href = this.zhujiaourl;
      }
    },
    //拉流
    pull(data) {
      let url = '/manage/bjylive/select/pull';
      this.$axios
        .get(url, {
          liveRoomId: data,
        })
        .then((res) => {
          if (res.state === 'success') {
            let ret = JSON.parse(res.body)
            console.log(ret);
            this.pullurl = ret.data.rtmp
            // const jsonObj = eval('(' + res.body + ')')
            //         console.log(jsonObj);
            //         this.teacherurl = jsonObj.clientLoginUrl
            //         this.zhujiaourl = jsonObj.assistantLoginUrl
            //         this.bookurl = jsonObj.viewUrl
            //         this.tuiliuurl = jsonObj.publishUrls
            //         this.hosturl = jsonObj.hostLoginUrl
            // // window.open(res.body);
            // // window.location.href = client_url + res.body + '&token=token&ts=ts';
          } else {
            this.$message.error('获取失败');
          }
        });

    },
    //进入直播间
    enterLive(role) {
      console.log(1);
      // let client_url = 'baijiacloud://urlpath=';
      let userName = role == 1 ? '高联老师' : '高联助教';
      let url = '/manage/bjylive/getWebLiveEnter';
      this.$axios
        .get(url, {
          liveroomId: this.liveroomId,
          userName: userName,
          userRole: role,
        })
        .then((res) => {
          if (res.state === 'success') {
            const jsonObj = eval('(' + res.body + ')')
            console.log(jsonObj);
            this.teacherurl = jsonObj.clientLoginUrl
            this.zhujiaourl = jsonObj.assistantLoginUrl
            this.bookurl = jsonObj.viewUrl
            this.tuiliuurl = jsonObj.publishUrls
            this.hosturl = jsonObj.hostLoginUrl
            // window.open(res.body);
            // window.location.href = client_url + res.body + '&token=token&ts=ts';
          } else {
            this.$message.error('获取失败');
          }
        });
    },
    dateFormat(fmt, date) {
      let ret;
      let opt = {
        'Y+': date.getFullYear().toString(), // 年
        'm+': (date.getMonth() + 1).toString(), // 月
        'd+': date.getDate().toString(), // 日
        'H+': date.getHours().toString(), // 时
        'M+': date.getMinutes().toString(), // 分
        'S+': date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp('(' + k + ')').exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'));
        }
      }
      return fmt;
    },
    playBack(data) {
      console.log(data);
      this.$router.push({
        path: '/product/playback',
        query: { liveroomId: data },
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.tab-title {
  padding-left: 35px;
  margin: 20px 0;
  font-size: 17px;
  color: #464646;
  font-weight: 600;
}

.tab-input {
  width: 350px;
}

 .el-form-item {
  margin-bottom: 20px;
}

.task-list {
  width: 80%;
  box-sizing: border-box;
  padding-left: 25px;

  span {
    float: left;
    line-height: 40px;
  }

  .task-span {
    margin-left: 15px;
  }

  .taskNum {
    color: #f5c319;
  }

  .task-left {
    float: left;
  }

  .task-right {
    float: right;
    margin-left: 10px;
  }

  .task-cursor {
    cursor: pointer;
  }
}

.list-group-item {
  cursor: move;
}

.ghost {
  border: 1px solid #aaaaaa;
}

.el-timeline-item {
  padding: 15px 0;
}

 .el-timeline {
  margin: 15px 0;
}

.scrollTop {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
  background: #f5c319;
  border-radius: 50%;
}</style>
