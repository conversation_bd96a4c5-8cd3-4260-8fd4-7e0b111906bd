<template>
  <el-row>
    <div>
      <el-col class="crumbs">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item><i class="el-icon-tickets"></i> 学员课程管理</el-breadcrumb-item>
        </el-breadcrumb>
      </el-col>
      <el-col style="padding-bottom: 0px;">
        <el-form :inline="true">
          <el-form-item class="handle-del mr10">
            <el-input placeholder="输入学员账号" v-model="condition.phone"></el-input>
          </el-form-item>
          <el-form-item class="handle-del mr10">
            <el-input placeholder="输入学员姓名" v-model="condition.realName"></el-input>
          </el-form-item>
          <el-form-item class="handle-del mr10">
            <el-input placeholder="输入课程名称" v-model="condition.productName"></el-input>
          </el-form-item>
          <el-form-item class="handle-del mr10">
            <el-select v-model="condition.type" placeholder="请选择排列顺序">
              <el-option label="升序" value="1"></el-option>
              <el-option label="倒序" value="0"></el-option>
              <el-option label="正常" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch()"><i class="el-icon-search"></i>搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button v-has="'product:open'" type="primary" @click="handleOpenCourse()">开通课程</el-button>
          </el-form-item>
          <el-form-item>
            <el-button  type="primary" @click="handleOpenCourse2()">申请课程</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="exportTable ()">导出成员模板</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col>
        <el-table :data="tableData" border class="table">
          <el-table-column prop="campusName" label="校区名称" width="120">
          </el-table-column>
          <el-table-column prop="phone" label="学员账号" width="120">
          </el-table-column>
          <el-table-column prop="realName" label="学员姓名" width="120">
          </el-table-column>
          <el-table-column prop="productName" label="课程名称" width="120">
          </el-table-column>
          <el-table-column prop="createTime" label="开通时间" width="120">
          </el-table-column>
          <el-table-column prop="creator" label="操作人" width="120">
          </el-table-column>
          <el-table-column label="操作" width="300" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="seeViewLog(scope.row)">观看记录</el-button>
              <el-button size="mini" type="danger" @click="seeCoursePlayRecord(scope.row)">课节观看统计</el-button>
              <el-button size="mini" type="danger" @click="sdelete(scope.row)">关闭</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24" class="toolbar">
        <my-page v-if="hackReset" @repeatQry="search()" :pagecondition="condition" :resultData="resultData" :needCount=true></my-page>
      </el-col>
    </div>
    <el-col>
      <el-dialog :title="titles" :visible.sync="isShowAddVisible">
        <el-col>
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="类目:">
              <el-cascader popper-class="theder" :value="category" ref="refHandle" :options="options" :props="{ checkStrictly: true }" :show-all-levels="true" @change="handleChange" clearable></el-cascader>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col>
          <el-form class="demo-form-inline">
            <el-form-item label="选择要开通的课程:">
              <el-select v-model="temp.productId" placeholder="请选择" @change="checkChange">
                <el-option v-for="item in products" :key="item.id" :label="item.productName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="要开通的课程(一次最多选择10个课程):">
              <el-tag v-for="tag in tags" :key="tag.productId" :closable="true" @close="handleClose(tag)" :type="tag.type">
                {{tag.productName}}
              </el-tag>
            </el-form-item>
            <el-form-item label="输入要开通学员手机号：" prop="loginNames">
              <vue-xlsx-table @on-select-file="handleSelectedFile">批量导入</vue-xlsx-table>
              <span class="tip1">一次最多导入200个账号</span>
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="多个用户以英文逗号间隔" maxlength="2399" show-word-limit v-model="mobiles">
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="短信通知：">
              <el-checkbox v-model="isSendMsg"></el-checkbox>
            </el-form-item> -->
            <el-form-item v-if="titles == '学员课程申请'" label="开通的课程是否征得课程所在片区主管的同意">
              <el-radio v-model="radio" label="0">是</el-radio>
              <el-radio v-model="radio" label="1">否</el-radio>
            </el-form-item>
          </el-form>
        </el-col>
        <div slot="footer" class="dialog-footer">
          <el-button @click="isShowAddVisible = false">取消</el-button>
          <el-button v-if="titles == '学员课程开通'" type="primary" :loading="listLoading" @click="addData()" class="title1" >确定</el-button>
          <el-button v-else type="primary" :loading="listLoading" @click="addData2()" :disabled="disabled">确定</el-button>
        </div>
      </el-dialog>
    </el-col>

  </el-row>
</template>
<!-- 引入组件库 -->
<script >
export default {
  name: 'productuser',
  data() {
    return {
      condition: {
          type:'2'
      },
      resultData: {},
      hackReset: true,
      tableData: [],
      products: [],
      productId: '',
      isSendMsg: false,
      props: { multiple: true },
      category: {},
      options: [],
      temp: {},
      listLoading: false,
      isShowAddVisible: false,
      tags: [],
      mobiles: '',

      radio:'',
      disabled:true,
      titles:'',
    };
  },
  created() {
    this.search();
    this.fetchCategory();
  },
  watch: {
    // 如果 `question` 发生改变，这个函数就会运行
    mobiles: function (newQuestion, oldQuestion) {
      this.temp.loginNames = this.mobiles;
    },
    radio:function(){
      if(this.radio != ""){
        this.disabled = false
      }
    },
    isShowAddVisible:function(){
      if(this.isShowAddVisible == false){
        this.disabled = true
        this.radio = ''
      }
    }
  },
  methods: {
    handleSearch() {
      this.hackReset = false;
      this.$nextTick(() => {
        this.hackReset = true;
        this.search();
      });
    },
    handleOpenCourse() {
      this.isShowAddVisible = true;
      this.products = [];
      this.tags = [];
      this.category = {};
      this.temp = {};
      this.mobiles = '';
      this.titles = "学员课程开通"
    },
    handleOpenCourse2() {
      this.isShowAddVisible = true;
      this.products = [];
      this.tags = [];
      this.category = {};
      this.temp = {};
      this.mobiles = '';
      this.titles = "学员课程申请"
    },
    addData() {
      this.listLoading = true;
      let params = Object.assign({}, this.temp);
      params.productList = this.tags;
      params.loginNames = this.mobiles;
      // params.isSendMsg = this.isSendMsg ? '1' : '2';//短信通知
      this.url = '/mgr/productuser/batchPurchasedProduct';
      this.$axios.post(this.url, params).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success(`成功`);
          this.listLoading = false;
          this.isShowAddVisible = false;
          this.search();
        } else {
          this.$message.error(res.data.errMsg);
          this.listLoading = false;
          let error = res.data.errMsg.split(":")[1].substring(0,11)
          this.$confirm("是否删除"+error , res.data.errMsg, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
              let mobilesarr = this.mobiles.split(",");
          var mobindex = mobilesarr.indexOf(error);
          if (mobindex > -1) {//大于0 代表存在，
            mobilesarr.splice(mobindex, 1);//存在就删除
          }
          this.mobiles = mobilesarr.join(",")
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });

          
          
        }
      });
    },
    addData2(){
      this.listLoading = true;
      let params = Object.assign({}, this.temp);
      // params.productList = this.tags;
      let classid = []
      let classname = []
      for (let i = 0; i < this.tags.length; i++) {
       classid.push(this.tags[i].productId)
       classname.push(this.tags[i].productName) 
      }
      classid = classid.toString();
      classname = classname.toString();
      params.productId = classid
      params.producName = classname
      params.loginNames = this.mobiles;
      params.proposerId = sessionStorage.getItem("userId");
      params.proposer = sessionStorage.getItem("loginName");
      params.campusId = sessionStorage.getItem("choseCampusId");
      params.state = this.radio
      console.log(params);
      this.url = '/mgr/productuser/apply';
      this.$axios.post(this.url, params).then((res) => {
        console.log(res);
        if (res.data.state === 'success') {
          this.$message.success(`成功`);
          this.listLoading = false;
          this.isShowAddVisible = false;
          this.search();
        } else {
          this.$message.error(res.data.errMsg);
          this.listLoading = false;
          let error = res.data.errMsg.split(":")[1].substring(0,11)
          this.$confirm("是否删除"+error , res.data.errMsg, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
              let mobilesarr = this.mobiles.split(",");
          var mobindex = mobilesarr.indexOf(error);
          if (mobindex > -1) {//大于0 代表存在，
            mobilesarr.splice(mobindex, 1);//存在就删除
          }
          this.mobiles = mobilesarr.join(",")
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        }
      });

    },
    search() {
      let params = this.condition;
      this.url = '/mgr/productuser/getProductList';
      this.$axios.post(this.url, params).then((res) => {
        if (res.data.state === 'success') {
          this.resultData = res.data.body;
          this.tableData = res.data.body.list;
        } else {
          this.$message.error('获取失败');
        }
      });
    },
    sdelete(row) {
      let temp = Object.assign({}, row);
      this.url = '/mgr/productuser/deleteProductUser';
      this.$axios.post(this.url, temp).then((res) => {
        if (res.data.state === 'success') {
          this.$message.success(`成功`);
          this.search();
        } else {
          this.$message.error(res.data.errMsg);
        }
      });
    },
    seeViewLog(row) {
      this.$router.push({
        path: '/product/usercourse',
        query: { productId: row.productId, userId: row.userId },
      });
    },
    seeCoursePlayRecord(row) {
      this.$router.push({
        path: '/product/usercourseplayrecord',
        query: { productId: row.productId, userId: row.userId },
      });
    },
    fetchCategory() {
      this.url = '/mgr/prodcutcategory/getCategorys';
      this.$axios.post(this.url, {}).then((res) => {
        if (res.data.state === 'success') {
          this.options = res.data.body;
        } else {
          this.$message.error(res.data.errMsg);
        }
      });
    },
    reset() {
      this.condition = {};
      this.temp = {};
    },
    handleChange(value) {
      this.temp = {};
      // this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
      let categoryId = value[value.length - 1];
      //获取课程
      let params = {};
      params.categorId = categoryId;
      this.url = '/mgr/product/getProductListByCategory';
      this.$axios.get(this.url, params).then((res) => {
        if (res.state === 'success') {
          this.products = res.body;
        } else {
          this.$message.error('获取失败');
        }
      });
    },

    handleClose(tag) {
      this.tags.splice(this.tags.indexOf(tag), 1);
    },
    checkChange(value) {
      if (this.tags.length >= 10) {
        this.$message.error('一次最多选择10个课程');
        return;
      }
      for (var m = 0; m < this.tags.length; m++) {
        if (this.tags[m].productId == value) {
          return;
        }
      }
      for (var n = 0; n < this.products.length; n++) {
        var product = this.products[n];
        if (value == product.id) {
          this.tags.push({ productId: product.id, productName: product.productName });
        }
      }
    },
    exportTable() {
      require.ensure([], () => {
        const { export_json_to_excel } = require('../../../static/js/Export2Excel');
        const tHeader = ['mobile'];
        const list = [{ mobile: '' }];
        const filterVal = ['mobile'];
        const data = list.map((v) => filterVal.map((j) => v[j]));
        export_json_to_excel(tHeader, data, '学员手机号');
      });
    },
    handleSelectedFile(convertedData) {
      let mobiles = '';
      let error = '';
      convertedData.body.map((v) => {
        console.log(v);
        if (v.mobile.trim().length != 11) {
          if (error == '') {
            error = v.mobile;
          } else {
            error = error + ',' + v.mobile;
          }
          return;
        }
        if (mobiles == '') {
          mobiles = v.mobile.trim();
        } else {
          mobiles = mobiles + ',' + v.mobile.trim();
        }
      });
      this.mobiles = mobiles.trim();
    },
  },
};
</script>
<style scoped>
.tip1{
  font-size: 12px;
  color: #666;
}

</style>
