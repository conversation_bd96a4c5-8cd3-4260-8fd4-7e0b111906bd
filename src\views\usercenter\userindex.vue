<template>
    <el-row v-loading="loading">
        <div>
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 高钻学员管理</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col style="padding-bottom: 0px;">
                <el-form :inline="true">
                    <el-form-item class="handle-del mr10">
                        <el-input placeholder="输入用户名" v-model="condition.phone"></el-input>
                    </el-form-item>
                    <el-select v-model="condition.state" placeholder="请选择学员状态" class="handle-select mr10">
                        <el-option v-for="item in status" :key="item.statusId" :label="item.label" :value=item.statusId>
                        </el-option>
                    </el-select>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch()"><i class="el-icon-search"></i>搜索</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-has="'student:add'" type="primary" @click="handleAdd()">新增</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="dialogVisible2 = true">批量开通</el-button>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" v-has="['create:users']" @click="dialogVisible3 = true">无校管家批量开通</el-button>
                    </el-form-item>


                    <!-- <input type="file" id="xsls" ref='ipt' accept=".xlsx, .xls" @change="getFileInfo" /> -->





                </el-form>
            </el-col>
            <el-col>
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="phone" label="用户名" width="120">
                    </el-table-column>
                    <el-table-column prop="realName" label="昵称/姓名" width="120">
                    </el-table-column>
                    <el-table-column prop="campusName" label="所属校区" width="120">
                    </el-table-column>
                    <el-table-column prop="postgraduateYears" label="考研年届" width="120">
                    </el-table-column>
                    <el-table-column prop="createtime" label="注册时间" width="120">
                    </el-table-column>
                    <el-table-column prop="state" label="状态" width="200">
                        <template slot-scope="scope">
                            {{ scope.row.state === 1 ? '开启' : '' }}
                            {{ scope.row.state === 2 ? '禁用' : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" align="center">
                        <template slot-scope="scope">
                            <el-button v-if="scope.row.state === 2" size="mini" type="success"
                                @click="handleModifyStatus(scope.row, 1)">开启
                            </el-button>
                            <el-button v-if="scope.row.state === 1" size="mini" type="danger"
                                @click="handleModifyStatus(scope.row, 2)">禁用
                            </el-button>
                            <el-button v-has="'student:edit'" size="mini" type="success"
                                @click="handleEdit(scope.row)">编辑
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
            <el-col :span="24" class="toolbar">
                <my-page v-if="hackReset" @repeatQry="search()" :pagecondition="condition" :resultData="resultData"
                    :needCount=true></my-page>
            </el-col>
        </div>
        <el-col>
            <el-dialog title="新增" :visible.sync="isShowAddVisible">
                <el-form label-width="80px" :model="temp" ref="dataForm">
                    <el-form-item label="缴费账号" prop="cname">
                        <el-input style="width:90%" v-model="jiaoyan" :disabled="inputxiao"></el-input>
                        <el-button type="success" @click=" getToken(); inquire()" :disabled="inputxiao">校验</el-button>
                    </el-form-item>
                    <el-form-item label="登陆账号" prop="cname">
                        <el-input v-model="temp.phone"></el-input>
                    </el-form-item>
                    <el-form-item label="真实姓名" prop="date">
                        <el-input v-model="temp.realName"></el-input>
                    </el-form-item>
                    <el-form-item label="考研年界" prop="date">
                        <el-select v-model="temp.postgraduateYears" placeholder="请选择" class="handle-select mr10">
                            <el-option v-for="item in postgraduateYearss" :key="item.postgraduateYears"
                                :label="item.postgraduateYears" :value=item.postgraduateYears>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-has="'check:judge'">
                        是否需要校管家校验:
                        <el-radio v-model="radio" label="1">是</el-radio>
                        <el-radio v-model="radio" label="0">否</el-radio>
                    </el-form-item>

                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="isShowAddVisible = false">取消</el-button>
                    <el-button type="primary" :disabled="disabled" :loading="listLoading" @click="addData(); search()"
                        class="title1">确定</el-button>
                </div>
            </el-dialog>
        </el-col>

        <el-col>
            <el-dialog title="编辑" :visible.sync="isEditVisible">
                <el-form label-width="80px" :model="temp" ref="dataForm">
                    <el-form-item label="真实姓名" prop="date">
                        <el-input v-model="temp.realName"></el-input>
                    </el-form-item>
                    <el-form-item label="登录账号" prop="phone">
                        <el-input v-model="temp.phone"></el-input>
                    </el-form-item>
                    <el-form-item label="考研年界" prop="date">
                        <el-select v-model="temp.postgraduateYears" placeholder="请选择" class="handle-select mr10">
                            <el-option v-for="item in postgraduateYearss" :key="item.postgraduateYears"
                                :label="item.postgraduateYears" :value=item.postgraduateYears>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所属校区" prop="date">
                        <el-select v-model="temp.campusId" placeholder="请选择">
                            <el-option v-for="item in campus" :key="item.campusId" :label="item.campusName"
                                :value="item.campusId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="isEditVisible = false">取消</el-button>
                    <el-button type="primary" :loading="listLoading" @click="editData(); search()"
                        class="title1">确定</el-button>
                </div>
            </el-dialog>
        </el-col>

        <!-- 弹框 -->
        <el-dialog title="有校管家未缴费学员未开通账号" :visible.sync="dialogVisible" width="30%">
            <span v-for="(v, i) in nomoneyList">{{ v }},</span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogVisible = false">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="批量开通" :visible.sync="dialogVisible2" width="30%">
            <el-upload class="upload-demo" ref="uploadExcel" :action="UploadUrl()" :limit=limitNum
                :before-upload="beforeUploadFile" :on-change="fileChange" :on-exceed="exceedFile"
                :on-success="handleSuccess" :on-error="handleError" :file-list="fileList" :data="dara"
                accept=".xls, .xlsx" multiple>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip" style="color: brown;">批量开通所有参数必传</div>
            </el-upload>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible2 = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible2 = false">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="批量开通" :visible.sync="dialogVisible3" width="30%">
            <el-upload class="upload-demo" ref="uploadExcel" :action="UploadUr2()" :limit=limitNum
                :before-upload="beforeUploadFile" :on-change="fileChange" :on-exceed="exceedFile"
                :on-success="handleSuccess" :on-error="handleError" :file-list="fileList" :data="dara"
                accept=".xls, .xlsx" multiple>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip" style="color: brown;">批量开通所有参数必传</div>
            </el-upload>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible3 = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible3 = false">确定</el-button>
            </span>
        </el-dialog>

    </el-row>


</template>

<script>
import env from '../../api/env'
export default {
    name: "campus",
    data() {
        return {
            condition: {},
            resultData: {},
            hackReset: true,
            tableData: [],
            campus: [],
            postgraduateYearss: [
                {
                    postgraduateYears: "2019"
                }, {
                    postgraduateYears: "2020"
                }, {
                    postgraduateYears: "2021"
                }, {
                    postgraduateYears: "2022"
                }, {
                    postgraduateYears: "2023"
                }, {
                    postgraduateYears: "2024"
                }, {
                    postgraduateYears: "2025"
                }, {
                    postgraduateYears: "2026"
                }, {
                    postgraduateYears: "2027"
                }
            ],
            temp: {
                loginName: '',
            },
            listLoading: false,
            isShowAddVisible: false,
            isEditVisible: false,
            status: [{
                statusId: 1,
                label: '开启'
            }, {
                statusId: 2,
                label: '禁用'
            }],
            tokenn: '',
            jiaoyan: '',
            disabled: true,
            radio: '',
            inputxiao: false,
            fileList: [],
            limitNum: 1,
            file: '',
            loading: false,
            dialogVisible: false,
            dialogVisible2: false,
            dialogVisible3: false,
            nomoneyList: [],
            dara: {
                "campus": 3333
            }
        }
    },
    created() {
        this.search();
        this.campus = JSON.parse(window.sessionStorage.getItem('permCampus'));
        this.getToken()

    },
    methods: {
        handleSearch() {
                this.search();
            },
        // 文件超出个数限制时的钩子
        exceedFile(files, fileList) {
            this.$notify.warning({
                title: '警告',
                message: `只能选择 ${this.limitNum} 个文件，当前共选择了 ${files.length + fileList.length} 个`
            });
        },
        // 文件状态改变时的钩子
        fileChange(file, fileList) {
            // this.form.file = file.raw
        },
        // 上传文件之前的钩子, 参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
        beforeUploadFile(file) {
            this.dara.campus = sessionStorage.getItem("choseCampusId")
            // const isJPG = file.type === 'application/pdf' || file.type === 'application/PDF';
            const isLt2M = file.size / 1024 / 1024 < 2000;

            // if (!isJPG) {
            //     this.$message.error('上传讲义只能是 指定 格式!');
            // }
            if (!isLt2M) {
                this.$message.error('上传讲义大小不能超过 2000M!');
            }
            return isLt2M;
        },
        // 文件上传成功时的钩子
        handleSuccess(res, file, fileList) {
            this.$notify.success({
                title: '成功',
                message: `文件上传成功`
            });
            console.log(res);


            if (res.state == "success") {
                if (res.body != "") {
                    this.dialogVisible = true;
                    this.nomoneyList = res.body;
                }

            } else {
                this.$message.error(res.errMsg);
            }
        },
        // 文件上传失败时的钩子
        handleError(err, file, fileList) {
            this.$notify.error({
                title: '错误',
                message: `文件上传失败`
            });
        },
        UploadUrl() {
            return env.baseURL + "/mgr/individual/import";
        },
        UploadUr2() {
            return env.baseURL + "/mgr/individual/import/user";
        },
        getFileInfo(event) {
            console.log(event);
            const imgObj = this.$refs.ipt.files[0];
            var formdata = new FormData();
            formdata.append('file', imgObj);
            formdata.append('campus', sessionStorage.getItem("choseCampusId"));
            this.UpdateXlsx(formdata);

        },


        UpdateXlsx(file) {
            this.loading = true;
            this.url = '/mgr/individual/import';
            this.$axios.post(this.url, file).then((res) => {

            })
        },

        getToken() {
            this.url = '/mgr/individual/getToken';
            this.$axios.get(this.url, {}).then((res) => {
                console.log(res);
                this.tokenn = res.body
            })
        },
        inquire() {

            this.url = '/mgr/individual/judgePhone';
            this.$axios.post(this.url, {
                phone: this.jiaoyan,
                token: this.tokenn
            })
                .then((res) => {
                    console.log(res);
                    if (res.data.state === "success") {
                        this.disabled = false
                        this.$message.success(`手机号已在校管家缴费`);
                    } else {
                        this.disabled = true
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        addData() {
            this.temp['xgjPhone'] = this.jiaoyan
            let params = Object.assign({}, this.temp)
            if (!checkPhone(params.phone)) {
                this.$message.error("登录账号为11位的手机号");
                return
            }
            console.log(params);
            this.listLoading = true;
            this.url = '/mgr/individual/regisCampusUser';
            this.$axios.post(this.url, params)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.$message.success(`成功`);
                        this.listLoading = false;
                        this.isShowAddVisible = false;
                        this.search();
                        this.reset();
                    } else {
                        this.$message.error(res.data.errMsg);
                        this.listLoading = false;
                    }
                })
        },
        editData() {
            this.listLoading = true;
            let params = Object.assign({}, this.temp)
            params.closeCampusPerm = true
            this.url = '/mgr/individual/editCampusUser';
            this.$axios.post(this.url, params)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.$message.success(`成功`);
                        this.listLoading = false;
                        this.isEditVisible = false;
                        this.temp.loginName = ''
                        this.search();
                        this.reset();
                    } else {
                        this.$message.error(res.data.errMsg);
                        this.listLoading = false;
                    }
                })
        },
        search() {
            let params = this.condition;
            this.url = '/mgr/individual/getIndividualList';
            this.$axios.post(this.url, params)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.resultData = res.data.body;
                        this.tableData = res.data.body.list;
                    } else {
                        this.$message.error('获取失败');
                    }
                })
        },
        handleModifyStatus(row, status) {
            let parm = {};
            let temp = Object.assign({}, row)
            parm.userId = temp.userId;
            parm.state = status;
            this.url = '/mgr/individual/changeUserStatus';
            this.$axios.post(this.url, parm)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.$message.success(`成功`);
                        this.search();
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },
        handleEdit(row) {
            this.isEditVisible = true;
            this.temp = Object.assign({}, row);
            this.temp.loginName = this.temp.phone
        },
        handleAdd(row) {
            this.isShowAddVisible = true;
            this.temp = {}
        },
        reset() {
            this.condition = {};
            this.temp = {};
            this.jiaoyan = '';
            this.radio = ''
        }
    },
    watch: {
        radio: function () {
            if (this.radio == "0") {
                this.jiaoyan = ''
                this.disabled = false
                this.inputxiao = true
            } else if (this.radio == "1") {

                this.disabled = true
                this.inputxiao = false
            }
        },
        isShowAddVisible: function () {
            if (this.isShowAddVisible == false) {
                this.disabled = true
                this.radio = ''
                this.inputxiao = false
            }
        }
    }
}

export function checkPhone(sValue) {
    var myreg = /^[1][0-9]{10}$/;
    if (!myreg.test(sValue)) {
        return false;
    } else {
        return true;
    }
}
</script>

<style scoped>
#xsls::file-selector-button {
    padding: 6px 10px;
    background-color: #1E9FFF;
    border: 1px solid #1E9FFF;
    border-radius: 3px;
    cursor: pointer;
    color: #fff;
    font-size: 12px;
}

#xsls {
    font-size: 0;
}

.upload-demo {
    text-align: center;
}
</style>
