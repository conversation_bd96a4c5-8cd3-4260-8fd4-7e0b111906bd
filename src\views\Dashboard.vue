<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="8">
                <el-card shadow="hover" class="mgb20" style="height:252px;">
                    <div class="user-info">
                        <img src="static/img/img.jpg" class="user-avator" alt="">
                        <div class="user-info-cont">
                            <div class="user-info-name">{{name}}</div>
                            <div>{{role}}</div>
                        </div>
                    </div>
                    <!--<div class="user-info-list">上次登录时间：<span>2018-01-01</span></div>-->
                    <!--<div class="user-info-list">上次登录地点：<span>济南</span></div>-->
                </el-card>
            </el-col>
            <!--<el-col :span="16" v-if="defaultflag">-->
                <!--<el-row :gutter="20" class="mgb20">-->
                    <!--<el-col :span="8">-->
                        <!--<el-card shadow="hover" :body-style="{padding: '0px'}">-->
                            <!--<div class="grid-content grid-con-1">-->
                                <!--<i class="el-icon-lx-people grid-con-icon"></i>-->
                                <!--<div class="grid-cont-right">-->
                                    <!--<div class="grid-num">{{userCountByDate}}</div>-->
                                    <!--<div>用户总量</div>-->
                                <!--</div>-->
                            <!--</div>-->
                        <!--</el-card>-->
                    <!--</el-col>-->
                    <!--<el-col :span="8">-->
                        <!--<el-card shadow="hover" :body-style="{padding: '0px'}">-->
                            <!--<div class="grid-content grid-con-2">-->
                                <!--<i class="el-icon-lx-notice grid-con-icon"></i>-->
                                <!--<div class="grid-cont-right">-->
                                    <!--<div class="grid-num">{{orderSum}}</div>-->
                                    <!--<div>购买金额</div>-->
                                <!--</div>-->
                            <!--</div>-->
                        <!--</el-card>-->
                    <!--</el-col>-->
                    <!--<el-col :span="8">-->
                        <!--<el-card shadow="hover" :body-style="{padding: '0px'}">-->
                            <!--<div class="grid-content grid-con-3">-->
                                <!--<i class="el-icon-lx-goods grid-con-icon"></i>-->
                                <!--<div class="grid-cont-right">-->
                                    <!--<div class="grid-num">{{commentSum}}</div>-->
                                    <!--<div>待审核评论</div>-->
                                <!--</div>-->
                            <!--</div>-->
                        <!--</el-card>-->
                    <!--</el-col>-->
                <!--</el-row>-->
            <!--</el-col>-->
        </el-row>
        <el-row :gutter="20"  v-if="defaultflag">
            <el-col :span="12">
                <el-card shadow="hover">
                    <schart ref="bar" class="schart" canvasId="bar" :data="sevenDaysUsers" type="bar" :options="options"></schart>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card shadow="hover">
                    <schart ref="line" class="schart" canvasId="line" :data="sevenDaysOrders" type="line" :options3="options2"></schart>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card shadow="hover">
                    <schart ref="bar" class="schart" canvasId="bar1" :data="sevenDaysUserOrders" type="bar" :options="options3"></schart>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card shadow="hover">
                    <schart ref="line" class="schart" canvasId="line1" :data="allUsers" type="line" :options="options4"></schart>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import Schart from 'vue-schart';
    import bus from '../../static/js/bus';
    export default {
        name: 'dashboard',
        data() {
            return {
                defaultflag:true,
                name: sessionStorage.getItem('loginName'),
                commentSum:'',
                orderSum:'',
                userCountByDate:'',
                sevenDaysOrders:[],
                sevenDaysUsers:[],
                sevenDaysUserOrders:[],
                allUsers:[],
                data: [{
                    name: '2018/09/04',
                    value: 1083
                },
                    {
                        name: '2018/09/05',
                        value: 941
                    },
                    {
                        name: '2018/09/06',
                        value: 1139
                    },
                    {
                        name: '2018/09/07',
                        value: 816
                    },
                    {
                        name: '2018/09/08',
                        value: 327
                    },
                    {
                        name: '2018/09/09',
                        value: 228
                    },
                    {
                        name: '2018/09/10',
                        value: 1065
                    }
                ],
                options: {
                    title: '最近七天每天的用户增长量',
                    showValue: true,
                    fillColor: 'rgb(45, 140, 240)',
                    bottomPadding: 30,
                    topPadding: 30
                },
                options2: {
                    title: '最近七天购买总金额',
                    fillColor: '#FC6FA1',
                    axisColor: '#008ACD',
                    contentColor: '#EEEEEE',
                    bgColor: '#F5F8FD',
                    bottomPadding: 30,
                    topPadding: 30
                }
                ,options3: {
                    title: '最近七天每天的购买用户',
                    showValue: true,
                    fillColor: 'rgb(45, 140, 240)',
                    bottomPadding: 30,
                    topPadding: 30
                },
                options4: {
                    title: '总注册数量',
                    fillColor: '#FC6FA1',
                    axisColor: '#008ACD',
                    contentColor: '#EEEEEE',
                    bgColor: '#F5F8FD',
                    bottomPadding: 30,
                    topPadding: 30
                }
            }
        },
        components: {
            Schart
        },
        computed: {
            role() {
                return this.name === 'admin' ? '超级管理员' : '普通用户';
            }
        },
        created(){
            let roles=JSON.parse(sessionStorage.getItem('roles'));

            for(var i=0;i<roles.length;i++){
                var roleName=roles[i].name;
                if(roleName=="teacher"){
                    this.defaultflag=false;
                    break;
                }
            }
            if(this.defaultflag){
                this.handleListener();
                this.changeDate();
                // this.getData();
            }
        },
        activated(){
            this.handleListener();
        },
        deactivated(){
            window.removeEventListener('resize', this.renderChart);
            bus.$off('collapse', this.handleBus);
        },
        methods: {
            changeDate(){
                const now = new Date().getTime();
                this.data.forEach((item, index) => {
                    const date = new Date(now - (6 - index) * 86400000);
                    item.name = `${date.getFullYear()}/${date.getMonth()+1}/${date.getDate()}`
                })
            },
            handleListener(){
                bus.$on('collapse', this.handleBus);
                // 调用renderChart方法对图表进行重新渲染
                window.addEventListener('resize', this.renderChart)
            },
            handleBus(msg){
                setTimeout(() => {
                    this.renderChart()
                }, 300);
            },
            renderChart(){
                this.$refs.bar.renderChart();
                this.$refs.line.renderChart();
            },
            getData(){
                this.url = '/manage/getManageHome';
                this.$axios.post(this.url, {}).then((res) => {
                    if (res.data.state === "success") {
                        /*const pageList = res.data
                        console.log(pageList)*/
                        this.commentSum = res.data.body.commentSum
                        this.orderSum = res.data.body.orderSum
                        this.userCountByDate = res.data.body.userCountByDate
                        this.sevenDaysOrders = res.data.body.sevenDaysOrders
                        this.sevenDaysUsers = res.data.body.sevenDaysUsers
                        this.allUsers = res.data.body.allUsers
                        this.sevenDaysUserOrders = res.data.body.sevenDaysUserOrders
                    } else {
                        this.$message.error('获取信息失败');
                    }
                })
            }
        }
    }

</script>


<style scoped>

    .grid-content {
        display: flex;
        align-items: center;
        height: 100px;
    }

    .grid-cont-right {
        flex: 1;
        text-align: center;
        font-size: 14px;
        color: #999;
    }

    .grid-num {
        font-size: 30px;
        font-weight: bold;
    }

    .grid-con-icon {
        font-size: 50px;
        width: 100px;
        height: 100px;
        text-align: center;
        line-height: 100px;
        color: #fff;
    }

    .grid-con-1 .grid-con-icon {
        background: rgb(45, 140, 240);
    }

    .grid-con-1 .grid-num {
        color: rgb(45, 140, 240);
    }

    .grid-con-2 .grid-con-icon {
        background: rgb(100, 213, 114);
    }

    .grid-con-2 .grid-num {
        color: rgb(45, 140, 240);
    }

    .grid-con-3 .grid-con-icon {
        background: rgb(242, 94, 67);
    }

    .grid-con-3 .grid-num {
        color: rgb(242, 94, 67);
    }

    .user-info {
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        border-bottom: 2px solid #ccc;
        margin-bottom: 20px;
    }

    .user-avator {
        width: 120px;
        height: 120px;
        border-radius: 50%;
    }

    .user-info-cont {
        padding-left: 50px;
        flex: 1;
        font-size: 14px;
        color: #999;
    }

    .user-info-cont div:first-child {
        font-size: 30px;
        color: #222;
    }

    .user-info-list span {
        margin-left: 70px;
    }

    .mgb20 {
        margin-bottom: 20px;
    }


    .schart {
        width: 100%;
        height: 300px;
    }

</style>
