<!--
 * @description: 
 * @author: guo
 * @Date: 2021-11-03 10:10:54
 * @LastEditors: 曾经我和上帝一起写了这段代码，现在只有上帝能读懂了
 * @LastEditTime: 2022-03-09 09:47:31
-->
<template>
  <div id="app">
    <router-view v-if="routerAlive"></router-view>
        <!-- <index v-if="routerAlive"/> -->
      <!-- /*只要在想要刷新的组件中加上v-if="routerAlive"即可*/ -->
  </div>
</template>

<script>
  export default{
  data(){
    return {
    routerAlive:true
    }
  },
  methods:{
    routerRefresh(){
      this.routerAlive = false;
    this.$nextTick(()=>{
      this.routerAlive = true;
    });
    }
  }
  }
</script>
<style>
@import '../static/css/main.css';
@import '../static/css/color-dark.css'; /*深色主题*/
/*@import "../static/css/theme-green/color-green.css";   浅绿色主题*/
</style>
