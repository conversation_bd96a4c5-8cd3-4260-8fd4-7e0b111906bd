import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

export default new Router({
    routes: [
        {
            path: '/',
            redirect: '/dashboard'
        },
        {
            path: '/',
            component: resolve => require(['../components/common/Home.vue'], resolve),
            meta: { title: '自述文件' },
            children:[
                {
                    path: '/dashboard',
                    component: resolve => require(['../views/Dashboard.vue'], resolve),
                    meta: { title: '系统首页' }
                },
                {
                    path: '/table',
                    component: resolve => require(['../components/page/BaseTable.vue'], resolve),
                    meta: { title: '基础表格' }
                },
                {
                    // 富文本编辑器组件
                    path: '/editor',
                    component: resolve => require(['../components/page/VueEditor.vue'], resolve),
                    meta: { title: '富文本编辑器' }
                },
                {
                    // markdown组件
                    path: '/markdown',
                    component: resolve => require(['../components/page/Markdown.vue'], resolve),
                    meta: { title: 'markdown编辑器' }
                },
                {
                    // 图片上传组件
                    path: '/upload',
                    component: resolve => require(['../components/page/Upload.vue'], resolve),
                    meta: { title: '文件上传' }
                },
                {
                    // vue-schart组件
                    path: '/charts',
                    component: resolve => require(['../components/page/BaseCharts.vue'], resolve),
                    meta: { title: 'schart图表' }
                },
                {
                    // 拖拽列表组件
                    path: '/drag',
                    component: resolve => require(['../components/page/DragList.vue'], resolve),
                    meta: { title: '拖拽列表' }
                },
                {
                    // 权限页面
                    path: '/permission',
                    component: resolve => require(['../components/page/Permission.vue'], resolve),
                    meta: { title: '权限测试', permission: true }
                } ,
                {
                    // 审核中心
                    path: '/tabs',
                    component: resolve => require(['../components/page/Tabs.vue'], resolve),
                    meta: { title: '审核中心' }
                } ,
                {
                    // 校区管理
                    path: '/campus/index',
                    component: resolve => require(['../views/campus/campusindex.vue'], resolve),
                    meta: { title: '校区管理' }
                } ,
                {
                    // 课程类别管理
                    path: '/product/category',
                    component: resolve => require(['../views/product/productcategory.vue'], resolve),
                    meta: { title: '课程类别管理' }
                } ,
                {
                    // 课程管理
                    path: '/product/index',
                    component: resolve => require(['../views/product/productindex.vue'], resolve),
                    meta: { title: '课程管理' }
                } ,
                {
                    // 讲义管理
                    path: '/product/handout',
                    component: resolve => require(['../views/product/producthandout.vue'], resolve),
                    meta: { title: '讲义管理' }
                } ,
                {
                    // 课节管理
                    path: '/product/course',
                    component: resolve => require(['../views/product/productcourse.vue'], resolve),
                    meta: { title: '课节管理' }
                } ,
                {
                    // 回放统计
                    path: '/product/playback',
                    component: resolve => require(['../views/product/playback.vue'], resolve),
                    meta: { title: '回放统计' }
                } ,
                //百家云点播
                {
                    path: '/byj/videoupload',
                    component: resolve => require(['../views/bjyvideo/VideoUpload.vue'], resolve),
                    meta: { title: '视频上传' }
                },
                {
                    path: '/addBjyLiveRoom',
                    component: resolve => require(['../views/bjylive/addLiveRoom.vue'], resolve),
                    meta: { title: '创建直播间' }
                },
                {
                    path: '/externalAddLiveRoom',
                    component: resolve => require(['../views/bjylive/externalAddLiveRoom.vue'], resolve),
                    meta: { title: '创建外部直播间' }
                },
                {
                    // 学员课程管理
                    path: '/product/user',
                    component: resolve => require(['../views/product/productuser.vue'], resolve),
                    meta: { title: '学员课程管理' }
                } ,
                {
                    // 观看记录
                    path: '/product/usercourse',
                    component: resolve => require(['../views/product/productusercourse.vue'], resolve),
                    meta: { title: '观看记录' }
                } ,
                {
                    // 课节观看统计
                    path: '/product/usercourseplayrecord',
                    component: resolve => require(['../views/product/usercourseplayrecord.vue'], resolve),
                    meta: { title: '课节观看统计' }
                } ,
                {
                    // 高钻学员管理
                    path: '/user/index',
                    component: resolve => require(['../views/usercenter/userindex.vue'], resolve),
                    meta: { title: '高钻学员管理' }
                } ,
                {
                    // 后台用户管理
                    path: '/consoleuser/index',
                    component: resolve => require(['../views/usercenter/manageuser.vue'], resolve),
                    meta: { title: '后台用户管理' }
                } ,
                {
                    // 学员任务管理
                    path: '/taskuser/index',
                    component: resolve => require(['../views/task/taskuser.vue'], resolve),
                    meta: { title: '学员任务管理' }
                } ,
                {
                    // 校长寄语
                    path: '/headmaster/index',
                    component: resolve => require(['../views/headmaster/index.vue'], resolve),
                    meta: { title: '校长寄语' }
                },
                {
                    // 校长寄语
                    path: '/answering/index',
                    component: resolve => require(['../views/answering/index.vue'], resolve),
                    meta: { title: '答疑列表' }
                },
                {
                    // 消息中心
                    path: '/message/index',
                    component: resolve => require(['../views/message/index.vue'], resolve),
                    meta: { title: '消息中心' }
                },
                {
                    // 成为代理申请
                    path: '/agent',
                    component: resolve => require(['../views/usercenter/agent.vue'], resolve),
                    meta: { title: '成为代理申请' }
                },
                {
                    // 调查问卷
                    path: '/questionnaire',
                    component: resolve => require(['../views/usercenter/questionnaire.vue'], resolve),
                    meta: { title: '调查问卷' }
                },

                 
            ]
        },
        {
            path: '/login',
            component: resolve => require(['../views/Login.vue'], resolve)
        },
        {
            path: '/404',
            component: resolve => require(['../views/404.vue'], resolve)
        },
        {
            path: '/403',
            component: resolve => require(['../views/403.vue'], resolve)
        },
        {
            path: '*',
            redirect: '/404'
        }
    ]
})
