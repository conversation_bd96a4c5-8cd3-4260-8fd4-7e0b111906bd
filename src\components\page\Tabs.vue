<template >
    <div class="">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-message"></i> 审核中心</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <el-tabs v-model="message">
                <!-- <el-tab-pane :label="`未读消息(${unread.length})`" name="first">
                    <el-table :data="unread" :show-header="false" style="width: 100%">
                        <el-table-column>
                            <template slot-scope="scope">
                                <span class="message-title">{{scope.row.title}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="date" width="180"></el-table-column>
                        <el-table-column width="120">
                            <template slot-scope="scope">
                                <el-button size="small" @click="handleRead(scope.$index)">标为已读</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="handle-row">
                        <el-button type="primary">全部标为已读</el-button>
                    </div>
                </el-tab-pane> -->
            </el-tabs>
            <el-collapse  @change="handleChange" accordion v-loading="loading">
                <el-collapse-item v-for="(v,i) in activeNames" :key="i" :name="i+1">
                    <template slot="title">
                        <div v-if="parseInt(v.types) == 2" class="redicon"></div> <i class="el-icon-s-custom icona"></i> <span class="same" :class="parseInt(v.types) == 2 ? '' : 'mor'">{{v.proposer}}的申请</span>
                        <span class="title-right">{{v.createTime}}</span>
                    </template>
                    <div class="main">
                      <div class="auditing">
                            <!-- {{v.proposer}}--{{v.loginNames}}--{{v.campus}}--{{v.producName}}--{{v.state}}--{{v.createTime}} -->
                            <div class="proposer">申请人</div>
                            <div class="phone p-name">学员账号</div>
                            <div class="campus">片区</div>
                            <div class="classes p-name">课程名称</div>
                            <div class="state">主管是否同意</div>
                            <div class="passTime p-name">通过时间</div>
                            <div class="proposer">审批人</div>
                            <div class="book">
                              
                          </div>
                        </div>
                    </div>
                    <div class="main">
                        <div class="auditing">
                            <!-- {{v.proposer}}--{{v.loginNames}}--{{v.campus}}--{{v.producName}}--{{v.state}}--{{v.createTime}} -->
                            <div class="proposer">{{v.proposer}}</div>
                            <div class="phone">
                              <div class="lankuang" v-for="(item,index) in v.loginNames" :key="index" >{{item}}</div>
                            </div>
                            <div class="campus">{{v.campus}}</div>
                            <div class="classes">
                              <div class="lankuang" v-for="(item,index) in v.producName" :key="index">{{item}}</div>
                            </div>
                            <div class="state">主管<span v-if="parseInt(v.state)">未</span><span v-else>已</span>同意</div>
                            <div class="passTime">{{v.passTime}}</div>
                            <div class="proposer">{{v.auditor}}</div>
                            <div class="book">
                              <el-button type="success" @click="yescheck()" :disabled="disabled">同意</el-button>
                              <el-button type="danger"  @click="open" :disabled="disabled">驳回</el-button>
                          </div>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
            
            </div>
    </div>
</template>
<script>
import { read } from 'ieee754';

  export default {
    data() {
      return {
          activeNames: [
        //     {
        //       key: 1,
        //       name:"aaa的申请"
        //   }, {
        //       key: 0,
        //       name:"bbb的申请"
        //   }, {
        //       key: 1,
        //       name:"ccc的申请"
        //   }
        ],
        message:'',
        redicon:true,
        rol:'1',
        id:'',
        disabled:'',
        loading:'',
        success:'success',
        danger:'danger'
        
      };
    },
    created(){
        this.checklist()
    },
    methods: {
      handleChange(val) {
        if(val !== ""){
            // this.activeNames[val-1].key = 0
            this.id = this.activeNames[val-1].id
            // if(this.activeNames[val-1].already == "1"){
            //     this.read()
            // }
            if(this.activeNames[val-1].types == "2"){
                this.disabled = false
            }else{
                this.disabled = true
                // this.success = 'info'
                // this.danger = 'info'
            }
        }
        
      },
    //   驳回弹框
    open() {
        this.$prompt('', '原因', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          this.nocheck(value)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });       
        });
      },
    //   审核列表
    checklist(){
        this.url = '/mgr/productuser/checkList';
      this.$axios.get(this.url, {}).then((res) => {
        console.log(res);
        if (res.state === 'success') {
          this.activeNames = res.body
          for (let i = 0; i < this.activeNames.length; i++) {
            this.activeNames[i].producName = this.activeNames[i].producName.split(",")
            this.activeNames[i].loginNames = this.activeNames[i].loginNames.split(",")
          }
          console.log("111",this.activeNames);

        } else {
          this.$message.error('获取失败');
        }
      });
    },
    // 修改已读未读状态
    read(){
        this.url = '/mgr/productuser/already';
      this.$axios.get(this.url, {"id":this.id}).then((res) => {
        if (res.state === 'success') {
            this.checklist()
        } else {
          this.$message.error('获取失败');
        }
      });
    },
    // 同意审核
    yescheck(){
      this.loading = true
        this.url = '/mgr/productuser/check';
      this.$axios.post(this.url, {"id":this.id,"type":"1"}).then((res) => {
        console.log(res);
        if (res.data.state === 'success') {
          this.disabled = true
          // this.success = 'info'
          // this.danger = 'info'
          this.loading = false
          this.$bua.$emit('noreadyone')
            this.$message.success('同意审核');
            this.checklist()
        } else {
          this.loading = false
          this.$message.error('获取失败');
        }
      });
    },
    // 驳回审核
    nocheck(value){
      this.loading = true
        this.url = '/mgr/productuser/check';
      this.$axios.post(this.url, {"id":this.id,"type":"0","cause":value}).then((res) => {
        console.log(res);
        if (res.data.state === 'success') {
          this.disabled = true
          // this.success = 'info'
          // this.danger = 'info'
          this.loading = false
          this.$bua.$emit('noreadyone')
            this.$message.success('驳回成功');
            this.checklist()
        } else {
          this.loading = false
          this.$message.error('驳回失败');
        }
      });
    },
  }
  }
</script>



<style scoped>
.message-title{
    cursor: pointer;
}
.handle-row{
    margin-top: 30px;
}
.redicon{
    position: absolute;
    background: red;
    width: 8px;
    height: 8px;
    border-radius: 50%;

}
.el-collapse-item{
    position: relative;
}
.mor{
    color:#999;
}
.same{
    margin-left: 20px;
}
.title-right{
    position: absolute;
    right: 40px;
}
.el-collapse-item__content{
    display: flex;
}
.main{
    display: flex;
}
.auditing{
    display: flex;
    width: 100%;
    height: 60px;
    line-height: 60px;
    border:1px solid rgb(217, 217, 217);
    margin-bottom: -1px;
}
.auditing>div{
    border-right: 1px solid rgb(217, 217, 217);
    padding: 0 5px;
}
.phone{
    width: 172px;
    height: auto;
    line-height: 22px;
    /* height: 60px; */
    word-break: break-all; word-wrap: break-word;
    overflow: auto;
    text-align: center;
    display: flex;
  align-items: center;
  justify-content: center;
  flex-flow: row wrap; 
    
}
.p-name{
  display: flex;
  align-items: center;
  justify-content: center;
 
}
.classes{
    width: 250px;
    height: 60px;
    line-height: 22px;
    /* height: 60px; */
    /* word-break: break-all; word-wrap: break-word;
    overflow: auto; */
    overflow-y: auto;
    text-align: center;
    display: flex;
  align-items: center;
  justify-content: center;
  flex-flow: row wrap; 
}
.classes::-webkit-scrollbar {
 -webkit-appearance: none;
}
.classes::-webkit-scrollbar:vertical {
 width: 5px;
 height: 30px;
}
.classes::-webkit-scrollbar-thumb {
 border-radius: 8px;
 border: 2px solid white;
 background-color: rgba(41, 41, 41, 0.3);
}
.phone::-webkit-scrollbar {
 -webkit-appearance: none;
}
.phone::-webkit-scrollbar:vertical {
 width: 5px;
 height: 30px;
}
.phone::-webkit-scrollbar-thumb {
 border-radius: 8px;
 border: 2px solid white;
 background-color: rgba(41, 41, 41, 0.3);
}
.book{
    width: 12%;
    line-height: 60px;
    text-align: center;
}
.icona{
    margin-left: 20px;
    color: #0096e5;
}
.campus{
  width: 5%;
  text-align: center;
}
.proposer{
  width: 8%;
  text-align: center;
}
.passTime{
  width: 132px;
  /* flex: 1; */
}
.state{
  flex: 1;
  text-align: center;
}
.lankuang{
  background-color: #ecf5ff;
    border-color: #d9ecff;
    display: inline-block;
    font-size: 12px;
    color: #409EFF;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    box-sizing: border-box;
    white-space: nowrap;
    margin-right: 2px;
    margin-bottom: 2px;
}
</style>

