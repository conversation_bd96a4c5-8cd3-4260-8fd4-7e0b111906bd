<template>
    <div>
        <div class="bjy-demo">
            <div class="button upload-wrapper add-file-button">
                <input type="file">
                上传新视频
            </div>

            <div class="jindu" v-if="jdxs">
                <el-progress :text-inside="true" :stroke-width="24" :percentage="uppro" status="success"></el-progress>
            </div>

            <!-- <div class="button btn-stop" @click="stop">
                停止上传
            </div> -->

            <!--<div class="button btn-continue"  @click="continu">-->
            <!--继续上传-->
            <!--</div>-->
        </div>
    </div>
</template>


<script>
let uploader;
export default {
    data() {
        return {
            form: '',
            courseId: 0,
            huidiaourl:'',
            names:'',
            daxiao:'',
            uppro:0,
            jdxs:false
        }
    },
    created() {
        this.courseId = this.$route.query.courseId;
    },
    mounted() {
        let that = this
        console.log(this.courseId)
        uploader = new CCH5Uploader(
            {
                timeout: 180000, // 设置超时处理时间 超时时间3分钟 超时会重试
                maxChunkSize: 1024 * 1024 * 4 , // 1M 最大不超过4M
                limitConcurrentUploads: 3, //并发上传文件数
                maxRetries: 10, // 文件上传失败重试次数,默认10次
                retryTimeout: 500, //重试延迟时间（毫秒）
                autoUpload: true, //是否添加文件后自动上传
                fileInput: $("input[type='file']"), //文件输入组件，必需要有的配置,可以是多个
                // filesContainer: $('.files'), //上传文件记录显示的容器

                // 重写获取分类名的方法,没有分类时不需要配置或者返回null
                // isAllowUploadCallback(row){
                //     console.log(row);
                // },
                getCategoryName: function () {
                    var hasCategory = $("#uploadCategory").length > 0;
                    if (!hasCategory) {
                        return null;
                    }
                    return $("#selectCategoryInput").attr("title");
                },
                //重写获取分类id的方法,没有分类时不需要配置或者返回null
                getCategoryId: function () {
                    var hasCategory = $("#uploadCategory").length > 0;
                    if (!hasCategory) {
                        return null;
                    }
                    return $("#uploadCategory").val();
                },
                //上传失败重试指定次数后依然失败后的回调
                uploadFail: function (recordNode, fileName, failMsg) {
                    errorMsgHandler(getShortStr(fileName, 10) + "文件上传失败,原因：" + failMsg);
                    if (recordNode.find('.rate').length > 0) {
                        if (recordNode.find("#pause").css("display") != "none") {
                            recordNode.find("#pause").click();
                        }
                        // 有暂停操作的示例
                        recordNode.find('.rate').html('<div class="tc" style="margin-top:6px;color:red;">上传失败</div>');
                    } else {
                        // 无暂停操作的示例
                        recordNode.find('.start').parent().append('<span style="color:red;">上传失败</span>');
                    }
                },

                //上传成功回调,可不做任何处理
                uploadSuccess: function (recordNode, fileName, ccvid) {
                    alert(fileName + '上传成功');
                    // console.log("对应的视频id为：" + ccvid);
                    // console.log(recordNode);
                },
                uppross:function(data){
                    // console.log(data);
                    that.jdxs = true;
                    var progress = Math.floor(data.loaded / data.total * 100);
                    that.uppro = progress

                },

                // 生成视频信息
                createuploadinfo: function (fileName, fileSize, categoryId) {
                    var video = {};
                    var encodeFileName = fileName;
                    var d = {
                        "title": encodeFileName,
                        "fileName": encodeFileName,
                        "filesize": fileSize,
                        "courseId":that.courseId
                    };

                    //默认分类时不传分类id参数
                    if (!!categoryId && categoryId != 0) {
                        d.categoryid = categoryId;
                    }

                    $.ajax({
                        url: "/eduvip/manage/bjydot/getUploadUrl",
                        async: false, //必须以同步的方式执行，否则后续操作拿不到接口返回的数据
                        type: "get",
                        data: d,
                        cache: false,
                        dataType: "json",
                        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                        error: function () {
                            video = {
                                "errMsg": "获取视频文件vid出错",
                                "errorType": "network"
                            };
                        },
                        success: function (data) {
                            console.log(data);
                            // if (data.errMsg) {
                            //     video.errMsg = data.errMsg;
                            //     return;
                            // }
                            const jsonObj = eval('(' + data.body + ')')
                           let url1 = jsonObj.uploadinfo.chunkurl.replace('http','https')
                            let url2 = jsonObj.uploadinfo.metaurl.replace('http','https')
                            console.log(url2);
                            video.success = true;
                            video.uid = jsonObj.uploadinfo.userid;
                            video.ccvid = jsonObj.uploadinfo.videoid;
                            video.servicetype = jsonObj.uploadinfo.servicetype;
                            video.name = fileName;
                            video.uri = url1;
                            video.metauri = url2;
                            video.size = fileSize;
                        }
                    });
                    return video;
                },
                // 是否允许上传回调，files为本次提交的视频文件
                isAllowUploadCallback: function (files) {
                    $.each(files, function (index, file) {
                        console.log(file);
                        // this.createuploadinfo(file.name,file.size)
                        this.names = file.name;
                        this.daxiao = file.size
                        // return that.$axios.get("/manage/bjydot/getUploadUrl",
                        //  {"fileName": file.name,"courseId":26065,"filesize":file.size}
                        // )
                        // .then((res) => {
                        //     if (res.state === "success") {
                        //         that.$message.success('上传成功');
                               
                        //             // id: res.body.video_id,
                        //             this.huidiaourl= jsonObj.uploadinfo.metaurl + "?uid=" + jsonObj.uploadinfo.userid + "&ccvid=" + jsonObj.uploadinfo.videoid + "&filename=" + data.videoName + "&filesize=" + data.videoSize +"&servicetype=" + jsonObj.uploadinfo.servicetype
                               
                        //     } else {
                        //         that.$message.error(res.errMsg);
                        //     }

                        // })
                    });
                    return true;
                }
            });
    },
    methods: {
        continu() {
            // 继续上传
            var data = uploader.currentFiles[0];
            uploader.resumeUpload(data);
        },
        stop() {
            uploader.stopFile(0);
        }
    }
}
</script>

<style type="text/css">
.bjy-desc {
    width: 600px;
    position: absolute;
    right: 0;
    border: 1px solid #ccc;
    padding: 10px;
}

.bjy-demo {
    width: 500px;
    float: left;
}

.bjy-demo .button {
    background-color: #37A4F5;
    display: inline-block;
    color: #fff;
    padding: 10px 15px;
    vertical-align: top;
}

.bjy-demo .upload-wrapper {
    position: relative;
    overflow: hidden;
}

.bjy-demo .upload-wrapper object,
.bjy-demo .upload-wrapper input[type="file"] {
    opacity: 0;
    filter: alpha(opacity=0);
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.item-list {
    font-size: 14px;
    color: #2e5580;
    padding: 10px;
    display: block;
    float: left;
    width: 100%;
}
.jindu{
    margin-top: 20px;
}

.item-name {
    display: inline-block;
    margin-right: 20px;
}</style>
