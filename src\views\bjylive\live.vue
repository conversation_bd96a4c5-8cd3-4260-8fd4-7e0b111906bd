<template>
    <div class="table">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-tickets"></i> 直播列表</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <el-table
                :data="tableData.slice((cur_page-1)*pagesize,cur_page*pagesize)"
                style="width: 100%">
                <el-table-column
                    label="课程名字"
                    width="150">
                    <template slot-scope="scope">
                        <div slot="reference" class="name-wrapper">
                            <span style="margin-left: 10px">{{ scope.row.productName }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="课时名字"
                    width="150">
                    <template slot-scope="scope">
                        <span style="margin-left: 10px">{{ scope.row.courseName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="直播间名字"
                    width="150">
                    <template slot-scope="scope">
                        <span style="margin-left: 10px">{{ scope.row.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="讲师名字"
                    width="150">
                    <template slot-scope="scope">
                        <span style="margin-left: 10px">{{ scope.row.teacherName }}</span>
                    </template>
                </el-table-column>
                <!--<el-table-column-->
                    <!--label="讲师密码"-->
                    <!--width="150">-->
                    <!--<template slot-scope="scope">-->
                        <!--<span style="margin-left: 10px">{{ scope.row.teacher_code }}</span>-->
                    <!--</template>-->
                <!--</el-table-column>-->
                <!--<el-table-column-->
                    <!--label="助教密码"-->
                    <!--width="150">-->
                    <!--<template slot-scope="scope">-->
                        <!--<span style="margin-left: 10px">{{ scope.row.admin_code }}</span>-->
                    <!--</template>-->
                <!--</el-table-column>-->
                <!--<el-table-column-->
                    <!--label="学生密码"-->
                    <!--width="150">-->
                    <!--<template slot-scope="scope">-->
                        <!--<span style="margin-left: 10px">{{ scope.row.student_code }}</span>-->
                    <!--</template>-->
                <!--</el-table-column>-->
                <el-table-column
                    label="开播时间"
                    width="220">
                    <template slot-scope="scope">
                        <i class="el-icon-time"></i>
                        <span style="margin-left: 10px">{{ scope.row.start_time }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="结束时间"
                    width="220">
                    <template slot-scope="scope">
                        <i class="el-icon-time"></i>
                        <span style="margin-left: 10px">{{ scope.row.end_time }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="operation" label="操作 ">
                    <template slot-scope="scope">
                        <el-button size="small" type="primary" @click="enterLive(scope.row.liveroomId,1)">讲师</el-button>
                        <el-button size="small" type="primary" @click="enterLive(scope.row.liveroomId,2)">助教</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    :current-page.sync="cur_page"
                    :page-size=pagesize
                    layout="prev, pager, next"
                    :total="total">
                </el-pagination>
            </div>
        </div>

    </div>
</template>

<script>
    export default {
        name: 'basetable',
        data() {
            return {
                url: '',
                tableData: [],
                cur_page: 1,
                pagesize:10,
                total:1,
            }
        },
        created() {
            this.getData();
        },
        methods: {
            // 分页导航
            handleCurrentChange(val) {
                this.cur_page = val;
            },
            // 获取 easy-mock 的模拟数据
            getData() {
                this.cur_page = 1;
                this.listLoading = true,
                    this.url = '/manage/getAllLiveClasses';
                this.$axios.post(this.url, {
                    loginName: sessionStorage.getItem('loginName')
                }).then((res) => {
                    if (res.data.state === "success") {
                        this.tableData = res.data.body
                        this.total = this.tableData.length
                    } else {
                        this.$message.error('获取教师列表失败');
                    }
                })
            },
            enterLive(liveroomId,role) {
                let client_url = 'baijiacloud://urlpath=';
                let userName=role==1?"高联老师":"高联助教"
                let url = '/manage/bjylive/getWebLiveEnter';
                this.$axios.get(url, {
                    "liveroomId": liveroomId,
                    "userId": sessionStorage.getItem("userId"),
                    "userName": userName,
                    "userRole": role
                }).then((res) => {
                    if (res.state === "success") {
                        // window.open(res.body);
                        window.location.href=client_url+res.body+"&token=token&ts=ts";
                    } else {
                        this.$message.error('获取失败');
                    }
                })
            },
        }
    }

</script>
