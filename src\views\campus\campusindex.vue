<template>
    <el-row>
        <div v-if="insertHtml1">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 校区管理</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col style="padding-bottom: 0px;">
                <el-form :inline="true">
                    <el-form-item>
                        <el-button type="primary" @click="addEdit()">新增</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col>
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="name" label="校区" width="120">
                    </el-table-column>

                    <el-table-column label="操作" width="350" align="center">
                        <template slot-scope="scope">
                            <el-button  type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button type="primary" @click="handleDelet(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
            <el-col :span="24" class="toolbar">
                <my-page v-if="hackReset"  @repeatQry="search()" :pagecondition="condition" :resultData="resultData" :needCount=true></my-page>

            </el-col>
        </div>


        <!-- 编辑弹出框 -->
        <div class="container" v-if="editHtml">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 校区管理</el-breadcrumb-item>
                    <el-breadcrumb-item>编辑</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <div class="form-box">
                <el-form label-width="80px" :model="temp" ref="dataForm">
                    <el-form-item label="名字">
                        <el-input v-model="temp.name"></el-input>
                    </el-form-item>
                </el-form>

                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="updateEdit">确 定</el-button>
                    <el-button @click="closeEdit">取 消</el-button>
                </span>
            </div>
        </div>

        <!-- 新增弹出框 -->
        <div class="container" v-if="insertHtml2">

            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 校区管理</el-breadcrumb-item>
                    <el-breadcrumb-item>增加</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <div class="form-box">
                <el-form ref="form" :model="temp" label-width="100px">
                    <el-form-item label="名称">
                        <el-input v-model="temp.name"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="createEdit">确 定</el-button>
                    <el-button @click="closeEdit">取 消</el-button>
                </span>
        </div>
    </el-row>
</template>

<script>
    export default {
        name: "campus",
        data() {
            return {
                condition:{},
                resultData:{},
                hackReset:true,
                tableData: [],
                temp:{},
                insertHtml2:false,
                insertHtml1:true,
                editHtml:false,
            }
        },
        created() {
            this.search();
        },
        methods: {
            handleSearch() {
                this.hackReset = false
                this.$nextTick(() => {
                    this.hackReset = true
                    this.search();
                })
            },
            handleEdit(row){
                this.insertHtml1=false;
                this.editHtml=true;
                this.temp = Object.assign({}, row);
            },
            closeEdit(){
                this.insertHtml2 = false;
                this.editHtml = false;
                this.insertHtml1=true;
                this.handleSearch();
            },
            addEdit(){
                this.temp={};
                this.insertHtml1=false;
                this.insertHtml2=true;
            },
            search(){
                let params = this.condition;
                this.url = '/mgr/campus/getCampusList';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.resultData=res.data.body;
                            this.tableData = res.data.body.list;
                        } else {
                            this.$message.error('获取失败');
                        }
                    })
            },
            updateEdit(){
                const tempData = Object.assign({}, this.temp);
                if(tempData.name==null||tempData.name==''){
                    this.$message.error('名字为空');
                    return;
                }
                this.url = '/mgr/campus/editCampus';
                this.$axios.post(this.url,tempData)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.insertHtml1=true;
                            this.editHtml=false;
                            this.search();
                            this.$message.success(`修改成功`);
                        } else {
                            this.$message.error('更改失败');
                        }
                    })
            },
            createEdit(){
                const tempData = Object.assign({}, this.temp);
                if(tempData.name==null||tempData.name==''){
                    this.$message.error('名字为空');
                    return;

                }

                this.url = '/mgr/campus/insertCampus';
                this.$axios.post(this.url,tempData)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.$message.success(`新建成功`);
                            this.insertHtml1=true;
                            this.insertHtml2=false;
                            this.search();
                        } else {
                            this.$message.error('新建失败');
                        }
                    })
            },
            handleDelet(row){
                this.$confirm('此操作将删除校区, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const tempData = Object.assign({}, row);

                    this.url = '/mgr/campus/deleteCampus';
                    this.$axios.post(this.url,tempData)
                        .then((res) => {
                            if (res.data.state === "success") {
                                this.$message.success(`删除成功`);
                                this.search();
                            } else {
                                this.$message.error('删除失败');
                            }
                        })
                });

            }
        }
    }
</script>

<style scoped>

</style>
