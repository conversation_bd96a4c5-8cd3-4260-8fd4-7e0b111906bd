<template>
    <el-row>
        <el-col class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-tickets"></i> 代理用户申请</el-breadcrumb-item>
            </el-breadcrumb>
        </el-col>
        <el-col style="padding-bottom: 0px;">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="手机号:">
                    <el-input v-model="condition.phone" placeholder="手机号"></el-input>
                </el-form-item>
                <el-form-item label="姓名:">
                    <el-input v-model="condition.name" placeholder="姓名"></el-input>
                </el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <!-- <el-button type="primary" icon="search" @click="exportTable">导出</el-button> -->
            </el-form>
        </el-col>

        <el-col>
            <el-table :data="tableData"  style="width: 100%" border v-loading="loading">
                <el-table-column prop="name" label="姓名" />
                <el-table-column prop="phone" label="手机号" />
                <el-table-column prop="school" label="学校"/>
                <el-table-column prop="fileUrl" label="简历链接"/>

                <!-- <el-table-column  label="操作">
                    <template slot-scope="scope">
                        <el-button  size="small" type="danger" @click="handleDelete(scope.row)">
                            删除</el-button>
                        <el-button  size="small" type="primary" @click="editDelivery(scope.row)">
                            详情</el-button>
                        <el-button size="mini" type="danger" @click="seeCoursePlayRecord(scope.row)">课节观看统计</el-button>
                    </template>
                </el-table-column> -->
            </el-table>
        </el-col>
        <el-col :span="24" class="toolbar">
            <!--<el-pagination background style="float:right;" @size-change="handleCurrentChange" @current-change="handleCurrentChange" :current-page.sync="pageNum" :page-size="100" layout="total, prev, pager, next" :total="1000"/>-->
            <my-page v-if="hackReset"  @repeatQry="search()" :pagecondition="condition" :resultData="resultData" :needCount=true></my-page>
        </el-col>
       
    </el-row>
</template>


<script>
    // import mypage from '../../components/common/Mypage.vue';
    let intParam={
        pageNum: "1",
                pageSize: "10"
    };
    export default {
        data() {
            return {
                condition:{
                    needCount:true
                },
                deliveryDatas:{},
                isShowEditVisible: false,
                listLoading: true,
                temp:{},
                resultData:{},
                tableData: [],
                loading:false,
                detailData:[],
                hackReset:true,
            }
        },
        created(){
 
                this.handleSearch()

        },
        methods: {
            exportTable() {
                require.ensure([], () => {
                    const { export_json_to_excel } = require('../../../static/js/Export2Excel');
                    const tHeader = ['系统用户', '课程名称', '购买时间'];
                    const filterVal = ['loginName', 'productName', 'createTime'];
                    const list = this.tableData;
                    const data = list.map(v => filterVal.map(j => v[j]));
                    export_json_to_excel(tHeader, data, '用户课程');
                })
            },
            handleSearch() {
                this.hackReset = false
                this.$nextTick(() => {
                    this.hackReset = true
                    this.search();
                })

            },
            handleSet(){
                this.condition={};
                this.search();
            },
            handleAdd(){

            },
            //获取列表
            search: function () {
                let that = this;
                let parms = that.condition;
                let url = '/mgr/agency/list';
                that.loading = true;
                this.$axios.post(url,parms)
                    .then((res) => {
                        that.loading = false;
                        this.resultData=res.data.body;
                        if (res.data.state === "success") {
                            this.tableData = res.data.body.list
                            this.listLoading = false
                        }
                    })
                    .catch(function (error) {
                        that.loading = false;
                        that.$message.error({
                            showClose: true,
                            message: "请求出现异常",
                            duration: 2000
                        });
                    });
            },
            handleDelete(row){
                this.$confirm('此操作将删除, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let that = this;
                    let params = that.condition;
                    let url = '/manage/userproduct/deleteUserProduct';
                    that.loading = true;
                    this.$axios.get(url,{
                        "userProductId":row.userProductId
                    }).then((res) => {
                        if (res.state === "success") {
                            this.search();
                        }else {
                            that.$message.error({
                                showClose: true,
                                message: "删除失败",
                                duration: 2000
                            });
                        }
                    })
                        .catch(function (error) {
                            that.$message.error({
                                showClose: true,
                                message: "请求出现异常",
                                duration: 2000
                            });
                        });
                });

            },
            seeCoursePlayRecord(row) {
                console.log(row);
                this.$router.push({
                    path: '/product/usercourseplayrecord',
                    query: {productId: row.productId, 
                        userIds: row.userIds
                    }
                });
            },
            editDelivery(row) {

                this.isShowEditVisible = true;
                let that = this;
                this.temp = Object.assign({}, row);
                let url = '/manage/userproduct/getDetailProducts';
                that.loading = true;
                this.$axios.post(url,{
                    userProductId:this.temp.userProductId,
                })
                    .then((res) => {
                        that.loading = false;
                        if (res.data.state === "success") {
                            this.detailData = res.data.body
                            this.listLoading = false
                        }
                    })
                    .catch(function (error) {
                        that.loading = false;
                        that.$message.error({
                            showClose: true,
                            message: "请求出现异常",
                            duration: 2000
                        });
                    });
                console.log(row)
            },

        },
        // components:{
        //     mypage
        // }
    }
</script>
