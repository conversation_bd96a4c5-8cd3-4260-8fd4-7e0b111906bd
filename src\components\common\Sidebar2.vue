<template>
    <div class="sidebar">
        <el-menu class="sidebar-el-menu" :default-active="defaultActiveIndex" :collapse="collapse" background-color="#324157"
            text-color="#bfcbd9" active-text-color="#20a0ff" unique-opened router @select="handleSelect">
            <template v-for="(item,index) in menus">
                <el-submenu v-if="!item.leaf" :index="index+''">
                    <template slot="title"><i :class="item.iconCls"></i><span slot="title">{{item.name}}</span></template>
                    <el-menu-item v-for="term in item.children" :key="term.path" :index="term.path" v-if="term.menuShow"
                        :class="$route.path==term.path?'is-active':''">
                        <i :class="term.iconCls"></i><span slot="title">{{term.name}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-menu-item v-else-if="item.leaf" :index="item.path+''" :class="$route.path==item.path?'is-active':''">
                    <i :class="item.iconCls"></i><span slot="title">{{item.name}}</span>
                </el-menu-item>
                
            </template>
            <template >
                <el-menu-item index="/message/index">
                    <span slot="title">消息中心</span>
                    <!-- <div class="btn-bell-badge"></div> -->
                </el-menu-item>
                
            </template>
        </el-menu>
    </div>
</template>

<script>
    import bus from '../common/bus';
    export default {
        data() {
            return {
                collapse: false,
                defaultActiveIndex: "0",
                menus: [],
                message:1
            }
        },
        methods: {
            handleSelect(index) {
                this.defaultActiveIndex = index;
            },
        },
        created(){
            // 通过 Event Bus 进行组件间通信，来折叠侧边栏
            bus.$on('collapse', msg => {
                this.collapse = msg;
            })
        },
        mounted() {
            this.menus = JSON.parse(window.sessionStorage.getItem('menus'));
            console.log(this.menus);
        }
    }
</script>

<style scoped>
    .sidebar{
        display: block;
        position: absolute;
        left: 0;
        top: 70px;
        bottom:0;
        overflow-y: scroll;
    }
    .sidebar::-webkit-scrollbar{
        width: 0;
    }
    .sidebar-el-menu:not(.el-menu--collapse){
        width: 250px;
    }
    .sidebar > ul {
        height:100%;
    }
    .el-menu-item{
        position: relative;
    }
    .btn-bell-badge{
        position: absolute;
        left: 75px;
        top: 18px;
        width: 8px;
        height: 8px;
        border-radius: 4px;
        background: #f56c6c;
    }
</style>
