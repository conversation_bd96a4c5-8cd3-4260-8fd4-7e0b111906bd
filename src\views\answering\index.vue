<!--
 * @description: 
 * @author: guo
 * @Date: 2021-11-05 11:00:49
 * @LastEditors: guo
 * @LastEditTime: 2021-11-05 13:50:44
-->
<template>
  <div class="answering">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="姓名：" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable size="small" />
      </el-form-item>
      <el-form-item label="手机号：" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable size="small" />
      </el-form-item>
      <el-form-item label="班主任姓名：" prop="academy">
        <el-input v-model="queryParams.academy" placeholder="请输入院校" clearable size="small" />
      </el-form-item>
      <el-form-item label="答疑题目数量：" prop="college">
        <el-input v-model="queryParams.college" placeholder="请输入学院全称" clearable size="small" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain size="mini" @click="handleExport">删除</el-button>
      </el-col> -->
    </el-row>

    <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%;margin-top:20px" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center">
      </el-table-column> -->
      <el-table-column prop="name" label="姓名" width="120" align="center">
      </el-table-column>
      <el-table-column prop="phone" label="手机号" width="120" align="center">
      </el-table-column>
      <el-table-column prop="teacher" label="班主任姓名" show-overflow-tooltip align="center">
      </el-table-column>
      <el-table-column prop="issue" label="答疑题目数量" show-overflow-tooltip align="center">
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { $record, $deleterecord } from '@/api/answering';
export default {
  name: 'Activity',
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        phone: null,
        academy: null,
        college: null,
        grade: null,
      },
      // 显示搜索条件
      showSearch: true,
      tableData: [],
      multipleSelection: [],
    };
  },
  created() {
    this.recordlist();
  },
  methods: {
    //   获取列表
    async recordlist() {
      const res = await $record();
      if (res.state === 'success') {
        this.tableData = res.body;
      }
    },
    // 删除列表
    async deleterecord(id) {
      const res = await $deleterecord(id);
      if (res.state === 'success') {
       this.recordlist();
      }
    },
    handleQuery() {},
    resetQuery() {},
    handleExport() {},
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleDelete(row) {
      this.deleterecord(row.id)
    },
  },
};
</script>
