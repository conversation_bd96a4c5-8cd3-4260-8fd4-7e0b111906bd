import Vue from 'vue';
import App from './App';
import router from './router';
import axios from 'axios';
import * as API from './api/index'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';    // 默认主题
// import '../static/css/theme-green/index.css';       // 浅绿色主题
import "babel-polyfill";
import './assets/iconfont/iconfont.css';
import VueDND from 'awe-dnd'
import vMypage from './components/common/Mypage.vue';
import vueXlsxTable from 'vue-xlsx-table'

import '../static/ueditor/ueditor.config.js'
import '../static/ueditor/ueditor.all.js'
import '../static/ueditor/lang/zh-cn/zh-cn.js'
import '../static/ueditor/ueditor.parse.min.js'
import './assets/newstyle.css'


Vue.use(ElementUI, { size: 'small' });
Vue.use(VueDND)
Vue.use(vueXlsxTable, {rABS: false}) //Browser FileReader API have two methods to read local file readAsBinaryString and readAsArrayBuffer, default rABS false
Vue.prototype.$axios = API;
// Vue.prototype.$axios = axios;
Vue.component('my-page', vMypage)
Vue.prototype.$bua = new Vue();

//使用钩子函数对路由awe-dnd进行权限跳转
router.beforeResolve((to, from, next) => {
    const role = sessionStorage.getItem('loginName');
    if(!role&&to.path== '/login'){
        next();
    }else if(!role){
        next('/login');
    }else if(to.meta.permission){
        // 如果是管理员权限则可进入，这里只是简单的模拟管理员权限而已
        role === 'admin' ? next() : next('/403');
    }else{
        // 简单的判断IE10及以下不进入富文本编辑器，该组件不兼容
        if(navigator.userAgent.indexOf('MSIE') > -1 && to.path === '/editor'){
            Vue.prototype.$alert('vue-quill-editor组件不兼容IE10及以下浏览器，请使用更高版本的浏览器查看', '浏览器不兼容通知', {
                confirmButtonText: '确定'
            });
        }else{
            next();
        }
    }
});

/**权限指令**/
Vue.directive('has', {
    inserted: function(el, binding) {
        if (!Vue.prototype.$_has(binding.value)) {
            el.parentNode.removeChild(el);
            // el.style.display="none"

        }
    }
});
Vue.directive('checkCampus', {
    inserted: function(el, binding) {
        if (!Vue.prototype.$_checkCampus(binding.value)) {
            el.parentNode.removeChild(el);
            //el.style.display="none"

        }
    }
});
//权限检查方法
Vue.prototype.$_has = function(value) {
    let isExist=false;
    let buttonpermsStr=sessionStorage.getItem("perms");
    if(buttonpermsStr==undefined || buttonpermsStr==null){
        return false;
    }

    let buttonperms=JSON.parse(buttonpermsStr);
    for(let i=0;i<buttonperms.length;i++){
        if(buttonperms[i]=="admin:all"||value.indexOf(buttonperms[i])>-1){
            isExist=true;
            break;
        }else {
        }
    }
    return isExist;
};
Vue.prototype.$_checkCampus = function(value) {
    console.log("campus:"+value)
    let isExist=false;
    let buttonpermsStr=sessionStorage.getItem("permCampus");
    if(buttonpermsStr==undefined || buttonpermsStr==null){
        return false;
    }

    let buttonperms=JSON.parse(buttonpermsStr);
    console.log(buttonperms)
    for(let i=0;i<buttonperms.length;i++){
        if(value.indexOf(buttonperms[i].campusId)>-1){
            isExist=true;
            break;
        }else {
        }
    }
    return isExist;
};

new Vue({
    router,
    render: h => h(App)
}).$mount('#app');
