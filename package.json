{"name": "vue-manage-system", "version": "3.1.0", "description": "基于Vue.js 2.x系列 + element-ui 内容管理系统解决方案", "author": "lin-xin <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "build": "node build/build.js", "build:dll": "webpack --config build/webpack.dll.conf.js"}, "dependencies": {"awe-dnd": "^0.3.1", "axios": "^0.15.3", "babel-polyfill": "^6.23.0", "Blob": "^0.10.0", "echarts": "^4.1.0", "element-ui": "2.13.0", "file-saver": "^1.3.8", "js-cookie": "^2.2.0", "mavon-editor": "^2.5.4", "sass": "^1.77.7", "vue": "^2.5.16", "vue-cropperjs": "^2.2.0", "vue-echarts-v3": "^1.0.19", "vue-quill-editor": "3.0.6", "vue-router": "^3.0.1", "vue-schart": "^1.0.0", "vue-xlsx-table": "^1.2.8", "vuedraggable": "^2.16.0", "xlsx": "^0.14.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "script-loader": "^0.7.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.0.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^3.0.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 5 versions", "not ie <= 8"]}