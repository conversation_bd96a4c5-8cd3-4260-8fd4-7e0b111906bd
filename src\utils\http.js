/*
 * @description: 
 * @author: guo
 * @Date: 2021-09-10 18:00:21
 * @LastEditors: guo
 * @LastEditTime: 2021-09-28 10:22:06
 */
import request from './request';
/**
 * <AUTHOR>
 * @description 请求函数工厂
 * @param {String} method
 * @param {String} url
 * @param {Object} params
 * @returns {Function}
 */
const requestFactory = (method, url, params, bolb) => {
  const config = {
    method,
    url,
  };
  params && (method === 'GET' ? config.params = params : config.data = params);
  bolb && (config.responseType = 'arraybuffer');
  console.log(url);
  return request(config);
};
const http = {
  get: (url, params, bolb) => requestFactory('GET', url, params, bolb),
  post: (url, params) => requestFactory('POST', url, params),
  put: (url, params) => requestFactory('PUT', url, params),
  delete: (url, params) => requestFactory('DELETE', url, params),
};
export default http;
