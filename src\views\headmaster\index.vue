<template>
    <el-row>
        <div v-if="insertHtml1">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 校长寄语</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col style="padding-bottom: 0px;">
                <el-form :inline="true">
                    <el-form-item>
                        <el-button type="primary" @click="addEdit()">新增</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col>
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="msg" label="校长寄语" width="120">
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间" width="120">
                    </el-table-column>
                    <el-table-column label="操作" width="350" align="center">
                        <template slot-scope="scope">
                            <el-button type="primary" @click="handleDelet(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </div>
        <!-- 新增弹出框 -->
        <div class="container" v-if="insertHtml2">

            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 校长寄语</el-breadcrumb-item>
                    <el-breadcrumb-item>增加</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <div class="form-box">
                <el-form ref="form" :model="temp" label-width="100px">
                    <el-form-item label="校长寄语">
                        <el-input type="textarea" v-model="temp.msg"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="createEdit">确 定</el-button>
                    <el-button @click="closeEdit">取 消</el-button>
                </span>
        </div>
    </el-row>
</template>

<script>
    export default {
        name: "campus",
        data() {
            return {
                condition:{},
                tableData: [],
                temp:{},
                insertHtml2:false,
                insertHtml1:true,
            }
        },
        created() {
            this.search();
        },
        methods: {
            handleSearch() {
                this.search();
            },
            addEdit(){
                this.temp={};
                this.insertHtml1=false;
                this.insertHtml2=true;
            },
            closeEdit(){
                this.insertHtml2 = false;
                this.insertHtml1=true;
                this.handleSearch();
            },
            search(){
                let params = this.condition;
                this.url = '/mgr/headmaster/message/get';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.tableData = res.data.body;
                        } else {
                            this.$message.error('获取失败');
                        }
                    })
            },
            createEdit(){
                const tempData = Object.assign({}, this.temp);
                if(tempData.msg==null||tempData.msg==''){
                    this.$message.error('寄语为空');
                    return;

                }

                this.url = '/mgr/headmaster/message/add';
                this.$axios.post(this.url,tempData)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.$message.success(`新建成功`);
                            this.insertHtml1=true;
                            this.insertHtml2=false;
                            this.search();
                        } else {
                            this.$message.error('新建失败');
                        }
                    })
            },
            handleDelet(row){
                this.$confirm('此操作将删除, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const tempData = Object.assign({}, row);

                    this.url = '/mgr/headmaster/message/delete';
                    this.$axios.get(this.url,
                        {"headmasterMsgId":tempData.id})
                        .then((res) => {
                            if (res.state === "success") {
                                this.$message.success(`删除成功`);
                                this.search();
                            } else {
                                this.$message.error('删除失败');
                            }
                        })
                });

            }
        }
    }
</script>

<style scoped>

</style>
