<template>
  <el-row>
    <div v-if="insertHtml1">
      <el-col class="crumbs">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item><i class="el-icon-tickets"></i>学员课程观看记录</el-breadcrumb-item>
        </el-breadcrumb>
      </el-col>
      <el-col v-for="(item,i) in tableData" :key="i">
        <div>{{item.categoryName}}</div>
        <el-table :data="item.userCourseChildren" border class="table">
          <el-table-column prop="courseName" label="课节名" width="120">
          </el-table-column>
          <el-table-column prop="lastviewdate" label="最后学习时间" width="120">
          </el-table-column>
          <el-table-column prop="studyTimes" label="已观看次数" width="120">
          </el-table-column>
          <el-table-column prop="" label="剩余观看次数" width="120">
            <template slot-scope="scope">
              {{scope.row.seeTimes-scope.row.studyTimes}}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="450" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </div>

    <!-- 编辑弹出框 -->
    <div class="container" v-if="editHtml">
      <el-col class="crumbs">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item><i class="el-icon-tickets"></i> 学员课程观看记录</el-breadcrumb-item>
          <el-breadcrumb-item>编辑次数</el-breadcrumb-item>
        </el-breadcrumb>
      </el-col>
      <div class="form-box">
        <el-form ref="form" label-width="100px">
          <el-form-item label="剩余观看次数">
            <el-input type="number" v-model="sxtime"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateEdit">确 定</el-button>
        <el-button @click="closeEdit">取 消</el-button>
      </span>
    </div>
  </el-row>
</template>
<!-- 引入组件库 -->
<script >
export default {
  name: 'usercourseindex',
  data() {
    return {
      condition: {},
      hackReset: true,
      tableData: [],
      productId: '',
      sxtime: 0,
      options: [],
      temp: {},
      listLoading: false,
      insertHtml1: true,
      insertHtml2: false,
      editHtml: false,
      isShowCopyVisible: false,
    };
  },
  watch: {
    $route: 'getPath',
  },
  created() {
     this.productId = this.$route.query.productId;
        this.userId = this.$route.query.userId;
    this.search();
  },
  mounted() {},
  methods: {
    getPath() {
      if (this.$route.path == '/product/usercourse') {
        this.productId = this.$route.query.productId;
        this.userId = this.$route.query.userId;
        this.search();
      }
    },
    handleSearch() {
      this.search();
    },
    handleEdit(row) {
      this.insertHtml1 = false;
      this.editHtml = true;
      this.temp = Object.assign({}, row);
      this.sxtime = this.temp.seeTimes - this.temp.studyTimes;
    },
    closeEdit() {
      this.insertHtml2 = false;
      this.editHtml = false;
      this.insertHtml1 = true;
      this.handleSearch();
    },
    updateEdit() {
      const tempData = Object.assign({}, this.temp);
      this.url = '/mgr/productuser/updateUserCourse';
      this.$axios
        .post(this.url, {
          id: tempData.id,
          seeTimes: parseInt(this.sxtime) + tempData.studyTimes,
        })
        .then((res) => {
          if (res.data.state === 'success') {
            this.insertHtml1 = true;
            this.editHtml = false;
            this.search();
            this.$message.success(`修改成功`);
          } else {
            this.$message.error(res.data.errMsg);
          }
        });
    },
    search() {
      let params = this.condition;
      this.url = '/mgr/productuser/getUserViewCourse';
      this.$axios
        .get(this.url, {
          productId: this.productId,
          userId: this.userId,
        })
        .then((res) => {
          if (res.state === 'success') {
            this.tableData = res.body;
            console.log(this.tableData);
          } else {
            this.$message.error(res.errMsg);
          }
        });
    },
  },
};
</script>
