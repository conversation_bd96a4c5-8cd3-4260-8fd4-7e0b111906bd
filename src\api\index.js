/**
 * Created by bootdo.
 */
import Env from './env';
import axios from 'axios'
import Vue from 'vue'

export let bus = new Vue()

axios.defaults.withCredentials = true;
// axios.defaults.headers.common['Authorization'] = AUTH_TOKEN;
//  axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';//配置请求头


//添加一个请求拦截器
axios.interceptors.request.use(
  config => {
    if (window.sessionStorage.getItem('access-token')) {
      config.headers.Authorization = window.sessionStorage.getItem('access-token');
    }
    if (window.sessionStorage.getItem('token')) {
        config.headers.token=window.sessionStorage.getItem('token');
        config.headers.deviceType="console";
        config.headers.loginName=window.sessionStorage.getItem('loginName');
      config.headers.Authorization = window.sessionStorage.getItem('token');
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
);
import router from '../router';
// 添加一个响应拦截器
axios.interceptors.response.use(function (response) {
    if (parseInt(response.data.code) === 401) {
      //未登录
      bus.$emit('goto', '/login')
    }
      if (response.data.errCode === "0101015") {
          //未登录
          router.replace({
              path: '/login',
              query: {redirect: router.currentRoute.fullPath}
          })
      }

  return response;
}, function (error) {
  // Do something with response error
  return Promise.reject(error);
});

//基地址
let base = Env.baseURL;

//测试使用
export const ISDEV = Env.isDev;


//通用方法
export const post = (url, params) => {
    if(params.closeCampusPerm==undefined||!(params.closeCampusPerm)){
        params.campusId=sessionStorage.getItem("choseCampusId");
    }
  return axios.post(`${base}${url}`, params).then(res => res)
}

export const get = (url, params) => {
    if(!(params.closeCampusPerm)){
        params.campusId=sessionStorage.getItem("choseCampusId");
    }
  return axios.get(`${base}${url}`, {
    params: params
  }).then(res => res.data)
}

export const put = (url, params) => {
  return axios.put(`${base}${url}`, params).then(res => res)
}

export const DELETE = (url, params) => {
  return axios.delete(`${base}${url}`, {
    params: params
  }).then(res => res.data)
}

export const PATCH = (url, params) => {
  return axios.patch(`${base}${url}`, params).then(res => res)
}
