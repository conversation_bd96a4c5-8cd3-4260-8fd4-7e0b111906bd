<template>
    <div class="login-wrap">
        <div class="ms-login">
            <div class="ms-title"><p style="font-size:27px;line-height:50px;color:#111;">高联教育管理后台</p></div>
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="0px" class="ms-content">
                <el-form-item prop="username">
                    <el-input v-model="ruleForm.username" placeholder="用户名"></el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input type="password" placeholder="密码" v-model="ruleForm.password" @keyup.enter.native="submitForm('ruleForm')"></el-input>
                </el-form-item>
                <div class="login-btn">
                    <el-button type="primary" @click="submitForm('ruleForm')">登录</el-button>
                </div>
                <p style="font-size:12px;line-height:50px;color:#999;">济南高联教育科技有限公司@版权所有</p>
            </el-form>
        </div>
    </div>
</template>

<script>
    import defines from '../../static/js/config'
    export default {
        data: function(){
            return {
                ruleForm: {
                    username: '',
                    password: ''
                },
                rules: {
                    username: [
                        { required: true, message: '请输入用户名', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入密码', trigger: 'blur' }
                    ]
                }
            }
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        let loginParam={
                            loginName:  this.ruleForm.username ,
                            passWord: this.ruleForm.password
                        };
                        //请求登录接口
                        let url = '/mgr/user/login';
                        this.$axios.post(url,loginParam ).then((res) => {
                            if (res.data.state === "success") {
                                let body=res.data.body;
                                sessionStorage.setItem('loginName',this.ruleForm.username);
                                sessionStorage.setItem("token",body.token);
                                sessionStorage.setItem("userId",body.userId);
                                sessionStorage.setItem("menus",JSON.stringify(body.menus))
                                sessionStorage.setItem("roles",JSON.stringify(body.roles))
                                sessionStorage.setItem("permCampus",JSON.stringify(body.permCampus))
                                sessionStorage.setItem("perms",JSON.stringify(body.perms));
                                defines.setname(this.ruleForm.username)
                                this.$router.push('/');
                            }else {
                                this.$message.error('用户名或密码错误，请重新登录！');
                            }
                        })
                    } else {
                        return false;
                    }


                });
            }
        }
    }
</script>

<style scoped>
    .login-wrap{
        position: relative;
        width:100%;
        height:100%;
        background-image: url(../assets/static/login-bg.jpg);
        background-size: 100%;
    }
    .ms-title{
        width:100%;
        line-height: 50px;
        text-align: center;
        font-size:20px;
        color: #fff;
        border-bottom: 1px solid #ddd;
    }
    .ms-login{
        position: absolute;
        left:50%;
        top:50%;
        width:350px;
        margin:-190px 0 0 -175px;
        border-radius: 5px;
        background: rgba(255,255,255, 0.3);
        overflow: hidden;
    }
    .ms-content{
        padding: 30px 30px;
    }
    .login-btn{
        text-align: center;
    }
    .login-btn button{
        width:100%;
        height:36px;
        margin-bottom: 10px;
    }
    .login-tips{
        font-size:12px;
        line-height:30px;
        color:#fff;
    }
</style>
