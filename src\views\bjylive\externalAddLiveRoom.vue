<!--
 * @description: 
 * @author: guo
 * @Date: 2021-11-03 10:10:54
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-05-22 14:44:39
-->
<template>
    <div class="container">
      <div class="form-box">
        <el-form ref="form" :model="form" label-width="120px">
          <el-form-item label="直播间名称">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="直播间描述">
            <el-input v-model="form.desc"></el-input>
          </el-form-item>
          <el-form-item label="直播间ID">
            <el-input v-model="form.roomId"></el-input>
          </el-form-item>
          <el-form-item label="直播间模板类型" label-width="118px">
            <el-select v-model="form.is_video_main" placeholder="请选择">
              <el-option v-for="item in is_video_mains" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否推流">
            <el-select v-model="form.is_long_term" placeholder="请选择">
              <el-option v-for="item in is_long_terms" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
  
          <el-form-item label="日期时间">
            <el-col :span="11">
              <el-date-picker type="datetime" placeholder="直播开始时间" v-model="form.livestarttime" style="width: 100%;"
                value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
            </el-col>
            <el-col class="line" :span="2">-</el-col>
            <el-col :span="11">
  
              <el-date-picker type="datetime" placeholder="直播结束时间" v-model="form.endTime" value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss" style="width: 100%;"></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">立即创建</el-button>
            <el-button @click="colse()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
  
    </div>
  </template>
  <script>
  import debounce from "@/utils/debounce"
  export default {
    name: 'addliveRoom',
    data() {
      return {
        form: {
                name: '',
                desc: '',
                // templatetype:'',//模板
                livestarttime: '',
                is_long_term: 0,
                is_video_main: 3,
                endTime: '',
                isPushLive:'0',
                roomId:''
        },
        is_long_terms: [{ "label": "不开启第三方", "value": 0 }, { "label": "开启第三方", "value": 1 }],
        is_video_mains: [{ "label": "视频直播", "value": 1 }, { "label": "视频直播+聊天互动+直播问答", "value": 2 }, { "label": "视频直播+聊天互动", "value": 3 }, { "label": "视频直播+聊天互动+直播文档", "value": 4 },
        { "label": "视频直播+聊天互动+直播文档+直播问答", "value": 5 }, { "label": "视频直播+直播问答", "value": 6 },],
        courseId: '',
        tyoe: '',
      };
    },
    created() {
      this.productId = this.$route.query.productId;
      this.campusId = this.$route.campusId
      // this.form.is_video_main.value = 0
    },
    methods: {
      onSubmit:debounce(function () {
        const tempData = Object.assign({}, this.form);
        tempData.templatetype = tempData.is_video_main
              tempData.foreignpublish  = tempData.is_long_term
              if(tempData.templatetype == 4 || tempData.templatetype == 5 ){
                  tempData.foreignpublish = 0
              }
        (tempData.courseId = this.$route.query.courseId), (tempData.source = '010'), (tempData.type = 2), (this.url = '/manage/bjylive/createWithoutLiveRoom');
        this.$axios.post(this.url, tempData).then((res) => {
          if (res.data.state === 'success') {
            this.listLoading = false;
            this.$message.success('创建成功');
            //清空输入框
            this.form.name = ""
            this.form.desc = ""
            this.form.endTime = ""
            this.form.livestarttime = ""
            this.form.roomId = ""
            this.$router.push({
              path: '/product/course',
              query: { productId: this.productId, campusId: this.campusId, close: '1' },
            });
            //this.tableList = this.tableList.filter(t => t.productId != this.temp.productId)
          } else {
            this.$message.error(res.data.errMsg);
            this.listLoading = false;
            this.isShowEditVisible = false;
          }
        });
     },500),
      // onSubmit() {
      //   const tempData = Object.assign({}, this.form);
      //   tempData.templatetype = tempData.is_video_main
      //         tempData.foreignpublish  = tempData.is_long_term
      //         if(tempData.templatetype == 4 || tempData.templatetype == 5 ){
      //             tempData.foreignpublish = 0
      //         }
      //   (tempData.courseId = this.$route.query.courseId), (tempData.source = '010'), (tempData.type = 2), (this.url = '/manage/bjylive/createLiveRoom');
      //   this.$axios.post(this.url, tempData).then((res) => {
      //     if (res.data.state === 'success') {
      //       this.listLoading = false;
      //       this.$message.success('创建成功');
      //       //清空输入框
      //       this.form.roomname = ""
      //       this.form.roomdesc = ""
      //       this.form.endTime = ""
      //       this.form.startTime = ""
      //       this.$router.push({
      //         path: '/product/course',
      //         query: { productId: this.productId, campusId: this.campusId, close: '1' },
      //       });
      //       //this.tableList = this.tableList.filter(t => t.productId != this.temp.productId)
      //     } else {
      //       this.$message.error(res.data.errMsg);
      //       this.listLoading = false;
      //       this.isShowEditVisible = false;
      //     }
      //   });
      // },
      colse() {
        this.$router.go(-1);
      },
    },
  };
  </script>
  <style></style>
  