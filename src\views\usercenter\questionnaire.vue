<template>
    <el-row>
        <div v-if="insertHtml1">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i>调查问卷</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col style="padding-bottom: 0px;">
                <el-form :inline="true">

                    <el-form-item class="handle-del mr10">
                        <el-input placeholder="输入学院手机号" v-model="condition.phone"></el-input>
                    </el-form-item>
                    <el-form-item class="handle-del mr10">
                        <el-date-picker type="date" placeholder="开始日期" v-model="condition.startTime" style="width:200px"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>
                    <el-form-item class="handle-del mr10">
                        <el-date-picker type="date" placeholder="截止日期" v-model="condition.endTime" style="width:200px"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleSearch()"><i class="el-icon-search"></i>搜索</el-button>
                    </el-form-item>

                </el-form>
            </el-col>
            <el-col>
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="phone" label="手机号" width="120">
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间" width="150">
                    </el-table-column>
                    <el-table-column prop="a" label="对本阶段课程设置的总体满意度" width="120">
                        <template slot-scope="scope">
                            <span>{{ getSatisfactionText(scope.row.a) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="b" label="对本阶段授课老师的总体满意度" width="120">
                        <template slot-scope="scope">
                            <span>{{ getSatisfactionText(scope.row.b) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="c" label="对本阶段班主任的耐心、责任心、态度、敬业程度的满意度" width="140">
                        <template slot-scope="scope">
                            <span>{{ getSatisfactionText(scope.row.c) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="d" label="对本阶段班主任的专业程度的满意度" width="120">
                        <template slot-scope="scope">
                            <span>{{ getSatisfactionText(scope.row.d) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="e" label="对本阶段班主任的学习计划的满意度" width="120">
                        <template slot-scope="scope">
                            <span>{{ getSatisfactionText(scope.row.e) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="f" label="对高联下一阶段教学服务提出建议" width="120">
                    </el-table-column>

                </el-table>
            </el-col>
            <el-col :span="24" class="toolbar">
                <my-page v-if="hackReset" @repeatQry="search()" :pagecondition="condition" :resultData="resultData"
                    :needCount=true></my-page>
            </el-col>
        </div>


    </el-row>
</template>
<!-- 引入组件库 -->
<script>
export default {
    name: "questionnaire",
    data() {
        // 自定义校验
        var douhao = (rule, value, callback) => {
            // callback 必须调用
            var reg = /^[^,]+$/gi
            if (!reg.test(value)) {
                callback(new Error('禁止使用英文逗号'));
            } else {
                callback();
            }
        };
        return {
            condition: {},
            resultData: {},
            hackReset: true,
            tableData: [],
            productId: '',
            options: [],
            options2: [],
            temp: {},
            listLoading: false,
            insertHtml1: true,
            insertHtml2: false,
            editHtml: false,
            isShowCopyVisible: false,
            campusList: [],
            rules: {
                productName: [{ required: true, message: "课程标题不能为空", trigger: "blur" },
                { validator: douhao, trigger: 'blur' }],
            },
        }
    },
    created() {
        this.search();
        // this.fetchCategory();
        // this. fetchCategory3();
    },
    computed: {
        satisfactionMap() {
            return {
                1: '满意',
                2: '非常满意',
                0: '不满意'
            };
        }
    },
    methods: {
        checkCampus(value) {
            let isExist = false;
            let buttonpermsStr = sessionStorage.getItem("permCampus");
            if (buttonpermsStr == undefined || buttonpermsStr == null) {
                return false;
            }

            let buttonperms = JSON.parse(buttonpermsStr);

            for (let i = 0; i < buttonperms.length; i++) {
                if (value.indexOf(buttonperms[i].campusId) > -1) {
                    isExist = true;
                    break;
                }
            }
            return isExist;
        },
        handleSearch() {
            this.hackReset = false
            this.$nextTick(() => {
                this.hackReset = true
                this.search();
            })
        },
        handleAddProduct() {
            this.temp = {};
            this.insertHtml1 = false;
            this.insertHtml2 = true;
        },
        handleEdit(row) {
            this.insertHtml1 = false;
            this.editHtml = true;
            this.temp = Object.assign({}, row);
        },
        closeEdit() {
            this.insertHtml2 = false;
            this.editHtml = false;
            this.insertHtml1 = true;
            this.handleSearch();
        },

        search() {
            let params = this.condition;
            params.closeCampusPerm = true;
            this.url = '/system/questionnaire/getList';
            this.$axios.post(this.url, params)
                .then((res) => {
                    if (res.data.state === "success") {
                        this.resultData = res.data.body;
                        this.tableData = res.data.body.list;
                        console.log(this.tableData);
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },

        getSatisfactionText(value) {
            return this.satisfactionMap[value] || '未知';
        },

        searchCampus() {
            let params = {};
            params.pageSize = 100000;
            this.url = '/mgr/campus/getCampusList';
            this.$axios.post(this.url, params)
                .then((res) => {
                    if (res.data.state === "success") {
                        let thembObject = {
                            id: '',
                            campusId: "",
                            name: '全部校区'
                        }
                        this.campusList = res.data.body.list;
                        this.campusList.unshift(thembObject)
                    } else {
                        this.$message.error(res.data.errMsg);
                    }
                })
        },




        reset() {
            this.condition = {};
            this.temp = {}
        },
        handleChange(value) {
            // this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            if (value.length == 0) {
                this.condition.categoryId = null
            } else {
                this.condition.categoryId = value[value.length - 1]
            }

        },
        handleChange2(value) {
            // this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            this.temp.categoryId = value[value.length - 1]
        },
        handleChange3(value) {
            this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            this.temp.newProductCategoryId = value[value.length - 1]
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input.is-disabled .el-input__inner {
    background-color: #ffffff;
    color: #606266;
    border-color: #ffffff;
}

.el-input.is-disabled .el-input__icon {
    display: none;
}

::v-deep .el-cascader-node>.el-radio {
    position: absolute;
    width: 150px;
    height: 100%;
}
</style>
