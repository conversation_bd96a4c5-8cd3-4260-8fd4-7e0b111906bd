<template>
    <el-row>
        <div>
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 回放统计</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <!-- <el-col style="padding-bottom: 0px;">
        <el-form :inline="true">
          <el-form-item class="handle-del mr10">
            <el-input placeholder="输入学员账号" v-model="condition.phone"></el-input>
          </el-form-item>
          <el-form-item class="handle-del mr10">
            <el-input placeholder="输入学员姓名" v-model="condition.realName"></el-input>
          </el-form-item>
          <el-form-item class="handle-del mr10">
            <el-input placeholder="输入课程名称" v-model="condition.productName"></el-input>
          </el-form-item>
          <el-form-item class="handle-del mr10">
            <el-select v-model="condition.type" placeholder="请选择排列顺序">
              <el-option label="升序" value="1"></el-option>
              <el-option label="倒序" value="0"></el-option>
              <el-option label="正常" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch()"><i class="el-icon-search"></i>搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button v-has="'product:open'" type="primary" @click="handleOpenCourse()">开通课程</el-button>
          </el-form-item>
          <el-form-item>
            <el-button  type="primary" @click="handleOpenCourse2()">申请课程</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="exportTable ()">导出成员模板</el-button>
          </el-form-item>
        </el-form>
      </el-col> -->
            <el-col>
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="viewerId" label="手机号" width="120">
                    </el-table-column>
                    <el-table-column prop="viewerName" label="昵称" width="120">
                    </el-table-column>
                    <el-table-column prop="watchTime" label="单次观看时间（秒）" width="220">
                    </el-table-column>
                    <el-table-column prop="enterTime" label="进入时间" width="220">
                    </el-table-column>
                    <el-table-column prop="leaveTime" label="退出时间" width="220">
                    </el-table-column>
                    <el-table-column prop="userRole" label="角色" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.userRole === 4 ? '学员' : '' }}
                            {{ scope.row.userRole === 2 ? '助教' : '' }}
                            {{ scope.row.userRole === 1 ? '主讲' : '' }}
                            {{ scope.row.userRole === 3 ? '主持人' : '' }}
                        </template>
                    </el-table-column>

                </el-table>
            </el-col>
            <el-col :span="24" class="toolbar">
                <my-page v-if="hackReset" @repeatQry="search()" :pagecondition="condition" :resultData="resultData"
                    :needCount=true></my-page>
            </el-col>
        </div>


    </el-row>
</template>
  <!-- 引入组件库 -->
<script >
export default {

    data() {
        return {
            condition: {
                liveRoomId: ''
            },
            hackReset: true,
            tableData: [],
            resultData: {},
        };
    },
    created() {
        this.condition.liveRoomId = this.$route.query.liveroomId
        this.search();

    },

    methods: {
        search() {
            let params = this.condition;
            this.url = '/manage/bjylive/selectLiveRoom';
            this.$axios.get(this.url, params).then((res) => {
                let ret = JSON.parse(res.body)
                ret.total = ret.count
                console.log(ret);
                if (ret.result == "OK") {
                    this.resultData = ret;
                    this.tableData = ret.userEnterLeaveActions;
                } else {
                    this.$message.error('获取失败');
                }
            });
        },
        fetchCategory() {
            this.url = 'manage/bjylive/selectLiveRoom';
            this.$axios.post(this.url, {}).then((res) => {
                if (res.data.state === 'success') {
                    this.options = res.data.body;
                } else {
                    this.$message.error(res.data.errMsg);
                }
            });
        },

    },
};
</script>
<style scoped>
.tip1 {
    font-size: 12px;
    color: #666;
}
</style>
  