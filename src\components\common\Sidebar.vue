<template>
    <div class="sidebar">
        <el-menu class="sidebar-el-menu" :default-active="onRoutes" :collapse="collapse" background-color="#324157"
            text-color="#bfcbd9" active-text-color="#20a0ff" unique-opened router>
            <template v-for="item in items">
                <template v-if="item.subs">
                    <el-submenu :index="item.index" :key="item.index">
                        <template slot="title">
                            <i :class="item.icon"></i><span slot="title">{{ item.title }}</span>
                        </template>
                        <el-menu-item v-for="(subItem,i) in item.subs" :key="i" :index="subItem.index">
                            {{ subItem.title }}
                        </el-menu-item>
                    </el-submenu>
                </template>
                <template v-else>
                    <el-menu-item :index="item.index" :key="item.index">
                        <i :class="item.icon"></i><span slot="title">{{ item.title }}</span>
                    </el-menu-item>
                </template>
            </template>
        </el-menu>
    </div>
</template>

<script>
    import bus from '../common/bus';
    export default {
        data() {
            return {
                collapse: false,
                items: [
                    {
                        icon: 'el-icon-menu',
                        index: 'dashboard',
                        title: '系统首页'
                    },
                    {
                        icon: 'el-icon-info',
                        index: 'usermanager',
                        title: '用户管理'
                    },
                    {
                        icon: 'el-icon-info',
                        index: 'rolemanage',
                        title: '角色管理'
                    },{
                        icon: 'iconfont icon-dingdan',
                        index: 'ordermanage',
                        title: '订单管理'
                    },{
                        icon: 'el-icon-info',
                        index: 'livemanage',
                        title: '直播管理'
                    },
                    {
                        icon: 'el-icon-info',
                        index: 'product',
                        title: '课程',
                        subs: [
                            {
                                icon: 'el-icon-info',
                                index: 'productmanage',
                                title: '课程管理'
                            }, {
                                icon: 'el-icon-info',
                                index: 'category',
                                title: '类别管理'
                            }, {
                                icon: 'el-icon-info',
                                index: 'teacher',
                                title: '教师管理'
                            }
                        ],
                    },
                    {
                        icon: 'el-icon-info',
                        index: 'models',
                        title: '组件库',
                        subs: [
                            {
                                index: 'table',
                                title: '基础表格'
                            },
                            {
                                index: 'tabs',
                                title: 'tab选项卡'
                            },
                            {
                                index: 'form',
                                title: '基本表单'
                            },
                            {
                                index: 'editor',
                                title: '富文本编辑器'
                            },
                            {
                                index: 'markdown',
                                title: 'markdown编辑器'
                            },
                            {
                                index: 'upload',
                                title: '文件上传'
                            },
                            {
                                index: 'charts',
                                title: 'schart图表'
                            },
                            {
                                index: 'drag',
                                title: '拖拽列表'
                            },
                            {
                                index: 'permission',
                                title: '权限测试'
                            },
                            {
                                index: '404',
                                title: '404页面'
                            }
                        ]
                    },
                    {
                        icon: 'el-icon-setting',
                        index: 'manageAdmin',
                        title: '管理员管理'
                    }
                ]
            }
        },
        computed:{
            onRoutes(){
                return this.$route.path.replace('/','');
            }
        },
        created(){
            // 通过 Event Bus 进行组件间通信，来折叠侧边栏
            bus.$on('collapse', msg => {
                this.collapse = msg;
            })
        }
    }
</script>

<style scoped>
    .sidebar{
        display: block;
        position: absolute;
        left: 0;
        top: 70px;
        bottom:0;
        overflow-y: scroll;
    }
    .sidebar::-webkit-scrollbar{
        width: 0;
    }
    .sidebar-el-menu:not(.el-menu--collapse){
        width: 250px;
    }
    .sidebar > ul {
        height:100%;
    }
</style>
