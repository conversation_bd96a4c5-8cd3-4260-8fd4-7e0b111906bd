<template>
  <el-row class="table">
    <h3 class="title">课程类别管理</h3>
      <el-col style="padding-bottom: 0px;">
          <el-form :inline="true">
              <el-form-item>
                  <el-button type="primary" @click="handleAddCategory(1,0)">新增</el-button>
              </el-form-item>
          </el-form>
      </el-col>
      <el-col>
          <el-table
              :data="tableData"
              style="width: 100%;margin-bottom: 20px;"
              row-key="categoryId"
              border
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
              <el-table-column prop="label" label="类别名" align="left"></el-table-column>
              <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                      <el-button v-if="scope.row.level<4" type="text" @click="handleAddCategory(scope.row.level+1,scope.row.categoryId)">新 增</el-button>
                      <el-button type="text" @click="handleEdite(scope.row)">编 辑</el-button>
                      <el-button type="text" @click="delCategory(scope.row)">删 除</el-button>
                  </template>
              </el-table-column>
          </el-table>
      </el-col>
      <el-col>
              <el-dialog title="新增" :visible.sync="isShowAddVisible">
                  <el-form label-width="80px" :model="temp" ref="dataForm">
                      <el-form-item label="类别名" prop="cname">
                          <el-input v-model="temp.categoryName"></el-input>
                      </el-form-item>
                  </el-form>
                  <div slot="footer" class="dialog-footer">
                      <el-button @click="isShowAddVisible = false">取消</el-button>
                      <el-button type="primary" :loading="listLoading" @click="addData()"   class="title1">确定</el-button>
                  </div>
              </el-dialog>
      </el-col>
      <el-col>
          <el-dialog title="编辑" :visible.sync="isShowEditVisible">
              <el-form label-width="80px" :model="temp" ref="dataForm">
                  <el-form-item label="类别名" prop="cname">
                      <el-input v-model="temp.categoryName"></el-input>
                  </el-form-item>
              </el-form>
              <div slot="footer" class="dialog-footer">
                  <el-button @click="isShowEditVisible = false">取消</el-button>
                  <el-button type="primary" :loading="listLoading" @click="editCategory()"   class="title1">确定</el-button>
              </div>
          </el-dialog>
      </el-col>
  </el-row>
</template>

<script>

    export default {
       name: "productcategory",
       data() {
            return {
                condition:{},
                categoryDialog: false,
                isShowAddVisible: false,
                isShowEditVisible: false,
                editData: null,
                temp:{},
                listLoading: false,
                tableData:[],
            }
       },
        created() {
           this.search();
        },
        methods: {
            handleAddCategory(level,parentId){
                if(level>4){
                    this.$message.error("只支持四级目录");
                    return
                }
                this.isShowAddVisible=true
                this.temp={};
                this.temp.level=level
                this.temp.parentId=parentId
            },
            handleEdite(row){
                this.temp={}
                row.categoryName=row.label;
                this.temp = Object.assign({}, row)
                this.isShowEditVisible=true
            },
            search(){
                let params = this.condition;
                this.url = '/mgr/prodcutcategory/getCategorys';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.tableData=res.data.body;
                        } else {
                            this.$message.error(res.data.errMsg);
                        }
                    })
            },
            addData(){
                this.listLoading = true;
                let params = Object.assign({}, this.temp)
                this.url = '/mgr/prodcutcategory/addCategory';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.$message.success(`成功`);
                            this.listLoading = false;
                            this.isShowAddVisible = false;
                            this.search();
                        } else {
                            this.$message.error(res.data.errMsg);
                            this.listLoading = false;
                        }
                    })
            },
            editCategory(){
                this.listLoading = true;
                let params = Object.assign({}, this.temp)
                this.url = '/mgr/prodcutcategory/editCategory';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.$message.success(`成功`);
                            this.listLoading = false;
                            this.isShowEditVisible = false;
                            this.search();
                        } else {
                            this.$message.error(res.data.errMsg);
                            this.listLoading = false;
                        }
                })
            },
            delCategory(row){
                let params = Object.assign({},row)
                this.url = '/mgr/prodcutcategory/deleteCategory';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.$message.success(`成功`);
                            this.search();
                        } else {
                            this.$message.error(res.data.errMsg);
                            this.listLoading = false;
                        }
                })
            }
        }
    };
</script>

