<template>
    <el-row>
        <el-col class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-tickets"></i> 英语考试系统管理</el-breadcrumb-item>
            </el-breadcrumb>
        </el-col>
        <el-col style="padding-bottom: 0px;">
            <el-form :inline="true" class="demo-form-inline"   width="30%">
                <el-form-item label="内容:">
                    <el-button type="primary" @click="getUEContent()">获取</el-button>
                    <el-button type="primary" @click="getUEContentTxt()">获取文本内容</el-button>
                </el-form-item>
                <el-form-item label="内容2:">

                    <Uediter  :config=config :id=ue1 ref="ue"></Uediter>
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>
</template>


<script>

    import Uediter from '../../components/ueditor/ueditor.vue';
    export default {
        name: 'editor',
        data: function(){
            return {
                config: {
                    initialFrameWidth: null,
                    initialFrameHeight: 350
                },
                ue1: "ue1",
            }
        },
        components: {
            Uediter
        },
        methods: {
            submit(){
                this.$message.success('提交成功！');
            },
            getUEContent(){
                let content = this.$refs.ue.getUEContent(); // 调用子组件方法
                this.$notify({
                    title: '获取成功，可在控制台查看！',
                    message: content,
                    type: 'success'
                });

            },
            getUEContentTxt() {
                let content = this.$refs.ue.getUEContentTxt(); // 调用子组件方法
                this.$notify({
                    title: '获取成功，可在控制台查看！',
                    message: content,
                    type: 'success'
                });
            }

        }
    }
</script>
