<template>
    <div class="table">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-tickets"></i> 管理员管理</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <div class="handle-box">
                <el-button type="success" @click="handleCreate">创建管理员</el-button>
            </div>
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="account" label="账号" sortable width="150">
                </el-table-column>
                <el-table-column prop="username" label="用户名" width="120">
                </el-table-column>
                <el-table-column prop="tel" label="联系电话" :formatter="formatter">
                </el-table-column>
                <el-table-column label="操作" width="180" align="center">
                    <template slot-scope="scope">
                        <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :total="1000">
                </el-pagination>
            </div>
        </div>

        <!-- 创建弹出框 -->
        <el-dialog title="新建" :visible.sync="createVisible" width="30%">
            <el-form ref="form" :model="form" label-width="100px">
                <el-form-item label="管理员账号">
                    <el-input v-model="form.account"></el-input>
                </el-form-item>
                <el-form-item label="管理员姓名">
                    <el-input v-model="form.username"></el-input>
                </el-form-item>
                <el-form-item label="管理员密码">
                    <el-input type="password" v-model="form.pwd"></el-input>
                </el-form-item>
                <el-form-item label="联系电话">
                    <el-input v-model="form.tel"></el-input>
                </el-form-item>

            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="createVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveCreate">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 删除提示框 -->
        <el-dialog title="提示" :visible.sync="delVisible" width="300px" center>
            <div class="del-dialog-cnt">删除不可恢复，是否确定删除？</div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="delVisible = false">取 消</el-button>
                <el-button type="primary" @click="deleteRow">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'adminListTable',
        data() {
            return {
                url: '/gljy/admin/getAdminAll',
                tableData: [],
                createVisible: false,
                delVisible: false,
                form: {
                    account: '',
                    username: '',
                    tel: '',
                    pwd: ''
                },
                idx: -1
            }
        },
        created() {
            this.getData();
        },
        methods: {
            // 获取 easy-mock 的模拟数据
            getData() {
                // 开发环境使用 easy-mock 数据，正式环境使用 json 文件
                // if (process.env.NODE_ENV === 'development') {
                //     this.url = '/ms/table/list';
                // };
                this.$axios.post(this.url, {
                }).then((res) => {
                    if (res.data.flag) {
                        this.tableData = res.data.adminlist;
                    }else {
                        this.$message.error('出错了~');
                    }                    
                })
            },
            formatter(row, column) {
                return row.tel;
            },
            handleCreate() {
                this.form = {};
                this.createVisible = true;
            },
            //保存新建
            saveCreate() {
                this.$axios.post("/gljy/admin/addAdmin?account="+this.form.account+"&username="+this.form.username+"&pwd="+this.form.pwd+"&telephone="+this.form.tel, {
                }).then((res) => {
                    if (res.data.flag) {
                        this.$message.success('添加成功');
                    }else {
                        this.$message.error('出错了~');
                    }                    
                });
                this.createVisible = false;
                this.getData();
            },
            handleDelete(index, row) {
                this.idx = index;
                this.delVisible = true;
            },
            // 确定删除
            deleteRow(){
                const item = this.tableData[this.idx];
                this.$axios.post("/gljy/admin/deleAdmin?id="+item.id, {
                }).then((res) => {
                    if (res.data.flag) {
                        this.$message.success('删除成功');
                    }else {
                        this.$message.error('出错了~');
                    }                    
                })
                this.delVisible = false;
                this.getData();
            }
        }
    }

</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }
    .del-dialog-cnt{
        font-size: 16px;
        text-align: center
    }
</style>
