<template>
    <div>
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-message"></i> 消息中心</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <el-collapse @change="handleChange" accordion>
                <el-collapse-item v-for="(v,i) in activeNames" :key="i" :name="i+1">
                    <template slot="title">
                        <span class="same" >{{v.proposer}}的申请</span>
                        <el-tag type="success" :type="v.type" :key="v.label">{{v.label}}</el-tag>
                        <span class="title-right">{{v.createTime}}</span>
                    </template>
                    <div class="main">
                      <div class="auditing">
                            <!-- {{v.proposer}}--{{v.loginNames}}--{{v.campus}}--{{v.producName}}--{{v.state}}--{{v.createTime}} -->
                            <div class="proposer">申请人</div>
                            <div class="phone p-name">学员账号</div>
                            <div class="campus">片区</div>
                            <div class="classes p-name">课程名称</div>
                            <div class="state">主管是否同意</div>
                            <div class="passTime p-name">通过时间</div>
                            <div class="proposer">审批人</div>
                            <div class="book">
                              <!-- 驳回原因 -->
                          </div>
                        </div>
                    </div>
                    <div class="main">
                        <div class="auditing">
                            <!-- {{v.proposer}}--{{v.loginNames}}--{{v.campus}}--{{v.producName}}--{{v.state}}--{{v.createTime}} -->
                            <div class="proposer">{{v.proposer}}</div>
                            <div class="phone">{{v.loginNames}}</div>
                            <div class="campus">{{v.campus}}</div>
                            <div class="classes">{{v.producName}}</div>
                            <div class="state">主管<span v-if="parseInt(v.state)">未</span><span v-else>已</span>同意</div>
                            <div class="passTime">{{v.passTime}}</div>
                            <div class="proposer">{{v.auditor}}</div>
                            <div class="cause">{{v.cause}}</div>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            activeNames: [
        //         {
        //       key: 1,
        //       name:"aaa的申请",
        //       type: 'warning', 
        //       label: '处理中'
        //   }, {
        //       key: 0,
        //       name:"bbb的申请",
        //       type: 'success', 
        //       label: '已通过'
        //   }, {
        //       key: 1,
        //       name:"ccc的申请",
        //       type: 'info', label: '已驳回'
        //   }
        ],
        message:'',
        redicon:true,
        rol:'1',
        }
    },
    created(){
        this.messagelist()
    },
    methods: {
      handleChange(val) {
        
      },
    //   消息列表
    messagelist(){
        this.url = '/mgr/productuser/selectList';
      this.$axios.get(this.url, {}).then((res) => {
        console.log(res);
        if (res.state === 'success') {
          this.activeNames = res.body
          for (let i = 0; i < this.activeNames.length; i++) {
            if(this.activeNames[i].types == "1"){
            this.activeNames[i].label = "已通过"
            this.activeNames[i].type = "success"
          }else if(this.activeNames[i].types == "0"){
            this.activeNames[i].label = "已驳回"
            this.activeNames[i].type = "info"
          }else{
            this.activeNames[i].label = "未处理"
            this.activeNames[i].type = "warning"
          }
            
          }
          console.log(this.activeNames);
        } else {
          this.$message.error('获取失败');
        }
      });
    }
    },

}
</script>

<style scoped>
    .message-title{
        cursor: pointer;
    }
    .handle-row{
        margin-top: 30px;
    }
    .redicon{
        position: absolute;
        background: red;
        width: 8px;
        height: 8px;
        border-radius: 50%;
    
    }
    .el-collapse-item{
        position: relative;
    }
    .mor{
        color:#999;
    }
    .same{
        margin-left: 20px;
    }
    .title-right{
        position: absolute;
        right: 40px;
    }
    .el-tag{
        margin-left: 20px;
    }
    .main{
    display: flex;
}
.auditing{
    display: flex;
    width: 100%;
    height: 60px;
    line-height: 60px;
    border:1px solid rgb(217, 217, 217);
    margin-bottom: -1px;
}
.auditing>div{
    border-right: 1px solid rgb(217, 217, 217);
    padding: 0 5px;
}
.phone{
    width: 177px;
    height: auto;
    line-height: 22px;
    /* height: 60px; */
    word-break: break-all; word-wrap: break-word;
    overflow: auto;
    text-align: center;
}
.p-name{
  display: flex;
  align-items: center;
  justify-content: center;
 
}
.classes{
    width: 187px;
    height: 60px;
    line-height: 22px;
    /* height: 60px; */
    /* word-break: break-all; word-wrap: break-word;
    overflow: auto; */
    overflow-y: auto;
    text-align: center;
}
.classes::-webkit-scrollbar {
 -webkit-appearance: none;
}
.classes::-webkit-scrollbar:vertical {
 width: 5px;
 height: 30px;
}
.classes::-webkit-scrollbar-thumb {
 border-radius: 8px;
 border: 2px solid white;
 background-color: rgba(41, 41, 41, 0.3);
}
.phone::-webkit-scrollbar {
 -webkit-appearance: none;
}
.phone::-webkit-scrollbar:vertical {
 width: 5px;
 height: 30px;
}
.phone::-webkit-scrollbar-thumb {
 border-radius: 8px;
 border: 2px solid white;
 background-color: rgba(41, 41, 41, 0.3);
}
.book{
    width: 13.5%;
    line-height: 60px;
    text-align: center;
}
.icona{
    margin-left: 20px;
    color: #0096e5;
}
.campus{
  width: 5%;
  text-align: center;
}
.proposer{
  width: 8%;
  text-align: center;
}
.passTime{
  width: 132px;
  /* flex: 1; */
}
.state{
  /* flex: 1; */
  width: 14%;
  text-align: center;
}
.cause{
    width: 13.5%;
    /* flex: 1; */
    text-align: center;
}
    </style>