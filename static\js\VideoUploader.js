!function(){var cc_util_mimeType={},cc_util_FiniteArray={},cc_function_supportLocalStorage={},cc_function_ratio={},cc_function_restrain={},cc_function_nextTick={},cc_function_allPromises={},cc_function_guid={},cc_function_around={},cc_function_extend={},cc_function_ucFirst={},cc_function_toBoolean={},cc_function_createEvent={},cc_function_replaceWith,cc_function_offsetParent={},cc_function_toNumber={},cc_util_json={},cc_function_offsetSecond={},cc_function_split={},cc_function_offsetMinute={},cc_util_localStorage={},cc_util_url={},cc_function_offsetHour={},cc_util_event={},cc_function_offsetDate={},cc_util_cookie={},cc_util_life={},cc_util_supload_supload={},cc_helper_AjaxUploader={},cc_helper_FlashUploader={},cc_ui_Uploader={},uploader_VideoUploader={},B,aa,Ja,exports,Ya,tb,Qb;function _typeof(e){if("function"==typeof Symbol&&"symbol"==typeof Symbol.iterator)_typeof=function(e){return typeof e};else _typeof=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};return _typeof(e)}cc_util_mimeType={html:"text/html",htm:"text/html",shtml:"text/html",xml:"text/xml",css:"text/css",js:"application/x-javascript",json:"application/json",atom:"application/atom+xml",rss:"application/rss+xml",mml:"text/mathml",txt:"text/plain",jad:"text/vnd.sun.j2me.app-descriptor",wml:"text/vnd.wap.wml",htc:"text/x-component",jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",tif:"image/tiff",tiff:"image/tiff",wbmp:"image/vnd.wap.wbmp",ico:"image/x-icon",jng:"image/x-jng",bmp:"image/x-ms-bmp",svg:"image/svg+xml",svgz:"image/svg+xml",webp:"image/webp",mp3:"audio/mpeg",wma:"audio/x-ms-wma",wav:"audio/x-wav",mid:"audio/midi",midd:"audio/midi",kar:"audio/midi",ogg:"audio/ogg",m4a:"audio/x-m4a",ra:"audio/x-realaudio",ram:"audio/x-pn-realaudio",mod:"audio/mod","3gp":"video/3gpp","3gpp":"video/3gpp",mp4:"video/mp4",mpeg:"video/mpeg",mpg:"video/mpeg",mov:"video/quicktime",webm:"video/webm",flv:"video/x-flv",m4v:"video/x-m4v",mng:"video/x-mng",asx:"video/x-ms-asf",asf:"video/x-ms-asf",wmv:"video/x-ms-wmv",avi:"video/x-msvideo",rm:"video/vnd.rn-realvideo",rmvb:"video/vnd.rn-realvideo",ts:"video/MP2T",dv:"video/x-dv",mkv:"video/x-matroska",jar:"application/java-archive",war:"application/java-archive",ear:"application/java-archive",hqx:"application/mac-binhex40",pdf:"application/pdf",ps:"application/postscript",eps:"application/postscript",ai:"application/postscript",rtf:"application/rtf",wmlc:"application/vnd.wap.wmlc",kml:"application/vnd.google-earth.kml+xml",kmz:"application/vnd.google-earth.kmz","7z":"application/x-7z-compressed",cco:"application/x-cocoa",jardiff:"application/x-java-archive-diff",jnlp:"application/x-java-jnlp-file",run:"application/x-makeself",pl:"application/x-perl",pm:"application/x-perl",prc:"application/x-pilot",pdb:"application/x-pilot",rar:"application/x-rar-compressed",rpm:"application/x-redhat-package-manager",sea:"application/x-sea",swf:"application/x-shockwave-flash",sit:"application/x-stuffit",tcl:"application/x-tcl",tk:"application/x-tcl",der:"application/x-x509-ca-cert",pem:"application/x-x509-ca-cert",crt:"application/x-x509-ca-cert",xpi:"application/x-xpinstall",xhtml:"application/xhtml+xml",zip:"application/zip",bin:"application/octet-stream",exe:"application/octet-stream",dll:"application/octet-stream",deb:"application/octet-stream",dmg:"application/octet-stream",eot:"application/octet-stream",iso:"application/octet-stream",img:"application/octet-stream",msi:"application/octet-stream",msp:"application/octet-stream",msm:"application/octet-stream",doc:"application/msword",xls:"application/vnd.ms-excel",ppt:"application/vnd.ms-powerpoint",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation"},cc_util_FiniteArray=function(exports){function e(e){$.extend(this,e),this.init()}var t=e.prototype;return t.init=function(){this.list=[]},t.push=function(e){var t=this,n=t.list;if(t.isFull())n.shift();if(n.length<t.max)n.push(e)},t.get=function(e){return this.list[e]},t.first=function(){return this.get(0)},t.last=function(){return this.get(this.list.length-1)},t.isFull=function(){return this.list.length===this.max},t.each=function(n){$.each(this.list,function(e,t){return n(t,e)})},t.size=function(){return this.list.length},t.clear=function(){this.list.length=0},e}(),cc_function_supportLocalStorage=function(){return void 0!==window.localStorage},cc_function_ratio=function(e,t){if(0<=e&&0<t)return e/t;else return 0},cc_function_restrain=function(e,t,n){if(e<t)e=t;else if(n<e)e=n;return e},cc_function_nextTick=function(e){var t=setTimeout(e,0);return function(){clearTimeout(t)}},cc_function_allPromises=function(e){var t=$.Deferred();return $.when.apply($,e).then(function(){t.resolve($.makeArray(arguments))},function(){t.reject($.makeArray(arguments))}),t},B=0,cc_function_guid=function(){return"cc_"+B++},cc_function_around=function(e,t,i,o){var n="string"===$.type(t),a=n?e[t]:e;if(!n)o=i,i=t;var r=function(){var e,t=$.makeArray(arguments);if($.isFunction(i))e=i.apply(this,t);if(!1!==e){if($.isFunction(a))e=a.apply(this,t);if($.isFunction(o)){t.push(e);var n=o.apply(this,t);if("undefined"!==$.type(n))e=n}return e}};return n?e[t]=r:r},cc_function_extend=function(n,e){if($.isPlainObject(e))$.each(e,function(e,t){if(!(e in n))n[e]=t})},cc_function_ucFirst=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},cc_function_toBoolean=function(e,t){if("boolean"!==$.type(e)){if(1===arguments.length)t=!!e;e=t}return e},cc_function_createEvent=function(e){if(e&&!e[$.expando])e="string"===$.type(e)||e.type?$.Event(e):$.Event(null,e);return e||$.Event()},cc_function_replaceWith=function(e,t){e=e[0],t=t[0],e.parentNode.replaceChild(t,e)},cc_function_offsetParent=function(e){if(e.is("body"))return e;for(var t,n=e.parent();!(t=n).is("body")&&"static"===t.css("position");)n=n.parent();return n},aa={int:parseInt,float:parseFloat},cc_function_toNumber=function(e,t,n){if("number"!==$.type(e)){var i=aa[n];if(i)e=i(e,10);else if($.isNumeric(e))e=+e;else e=NaN}return isNaN(e)?t:e},cc_util_json=function(exports){if("object"!==("undefined"==typeof JSON?"undefined":_typeof(JSON)))JSON={};return function(){"use strict";var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta,rep;function f(e){return e<10?"0"+e:e}function this_value(){return this.valueOf()}if("function"!=typeof Date.prototype.toJSON)Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value;function quote(e){return rx_escapable.lastIndex=0,rx_escapable.test(e)?'"'+e.replace(rx_escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var n,i,o,a,r,c=gap,u=t[e];if(u&&"object"===_typeof(u)&&"function"==typeof u.toJSON)u=u.toJSON(e);if("function"==typeof rep)u=rep.call(t,e,u);switch(_typeof(u)){case"string":return quote(u);case"number":return isFinite(u)?String(u):"null";case"boolean":case"null":return String(u);case"object":if(!u)return"null";if(gap+=indent,r=[],"[object Array]"===Object.prototype.toString.apply(u)){for(a=u.length,n=0;n<a;n+=1)r[n]=str(n,u)||"null";return o=0===r.length?"[]":gap?"[\n"+gap+r.join(",\n"+gap)+"\n"+c+"]":"["+r.join(",")+"]",gap=c,o}if(rep&&"object"===_typeof(rep)){for(a=rep.length,n=0;n<a;n+=1)if("string"==typeof rep[n])if(o=str(i=rep[n],u))r.push(quote(i)+(gap?": ":":")+o)}else for(i in u)if(Object.prototype.hasOwnProperty.call(u,i))if(o=str(i,u))r.push(quote(i)+(gap?": ":":")+o);return o=0===r.length?"{}":gap?"{\n"+gap+r.join(",\n"+gap)+"\n"+c+"}":"{"+r.join(",")+"}",gap=c,o}}if("function"!=typeof JSON.stringify)meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(e,t,n){var i;if(indent=gap="","number"==typeof n)for(i=0;i<n;i+=1)indent+=" ";else if("string"==typeof n)indent=n;if((rep=t)&&"function"!=typeof t&&("object"!==_typeof(t)||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})};if("function"!=typeof JSON.parse)JSON.parse=function(text,reviver){var j;function walk(e,t){var n,i,o=e[t];if(o&&"object"===_typeof(o))for(n in o)if(Object.prototype.hasOwnProperty.call(o,n))if(void 0!==(i=walk(o,n)))o[n]=i;else delete o[n];return reviver.call(e,t,o)}if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text))text=text.replace(rx_dangerous,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)});if(rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")}}(),JSON}(cc_util_json),cc_function_offsetSecond=function(e,t){if("date"===$.type(e))e=e.getTime();return new Date(e+1e3*t)},cc_function_split=function(e,t){var n=[];if("number"===$.type(e))e=""+e;if(e&&"string"===$.type(e))$.each(e.split(t),function(e,t){if(t=$.trim(t))n.push(t)});return n},Ja=cc_function_offsetSecond,cc_function_offsetMinute=function(e,t){return Ja(e,60*t)},cc_util_localStorage=function(exports){var e=cc_function_supportLocalStorage();if(exports.support=e)exports.set=function e(t,n){if($.isPlainObject(t))$.each(t,e);else try{localStorage[t]=n}catch(e){}},exports.get=function(e){var t;try{t=localStorage[e]}catch(e){}return t},exports.remove=function(e){try{localStorage.removeItem(e)}catch(e){}};else exports.set=exports.get=exports.remove=$.noop;return exports}(cc_util_localStorage),exports=cc_util_url,Ya=cc_function_split,exports.parseQuery=function(e){var o={};if("string"===$.type(e)&&0<=e.indexOf("=")){var t=e.charAt(0),n="?"===t||"#"===t?1:0;if(0<n)e=e.substr(n);$.each(Ya(e,"&"),function(e,t){var n=t.split("=");if(2===n.length){var i=$.trim(n[0]);if(i)o[i]=decodeURIComponent(n[1])}})}return o},exports.stringifyQuery=function(e){var n=[];if($.isPlainObject(e))$.each(e,function(e,t){n.push(e+"="+encodeURIComponent(t))});return n.join("&")},exports.parse=function(e){if(null==e)e=document.URL;var t=document.createElement("a");t.href=e,e=t.href;var n="";if(t.protocol&&t.host)n=t.protocol+"//"+t.host;else if(/^(http[s]?:\/\/[^\/]+)(?=\/)/.test(e))n=RegExp.$1;var i=n.split(":");if(0===n.indexOf("http:")&&3===i.length&&80==i[2])i.length=2,n=i.join(":");var o=t.pathname;if(o&&"/"!==o.charAt(0))o="/"+o;return{origin:n,pathname:o,search:t.search,hash:t.hash}},exports.mixin=function(e,t,n){if("boolean"===$.type(t)&&2===arguments.length)n=t,t=null;if(null==t)t=document.URL;var i=exports.parse(t),o=exports.parseQuery(n?i.hash:i.search);if($.extend(o,e),o=$.param(o),t=i.origin+i.pathname,n)t+=i.search;else if(o)t+="?"+o;if(!n)t+=i.hash;else if(o)t+="#"+o;return t},cc_util_url=exports,tb=cc_function_offsetMinute,cc_function_offsetHour=function(e,t){return tb(e,60*t)},cc_util_event=function(exports){var t=cc_function_extend,n=cc_function_createEvent,i={get$:function(){if(!this.$)this.$=$({});return this.$},on:function(e,t,n){return this.get$().on(e,t,n),this},once:function(e,t,n){return this.get$().one(e,t,n),this},off:function(e,t){return this.get$().off(e,t),this},emit:function(e,t){return e=n(e),this.get$().trigger(e,t),e}};return exports.extend=function(e){t(e,i)},exports}(cc_util_event),Qb=cc_function_offsetHour,cc_function_offsetDate=function(e,t){return Qb(e,24*t)},cc_util_cookie=function(exports){var r=cc_function_split,o=cc_function_offsetDate;function i(e,t,n){var i=n.expires;if($.isNumeric(i))i=o(new Date,i);document.cookie=[encodeURIComponent(e),"=",encodeURIComponent(t),i?";expires="+i.toUTCString():"",n.path?";path="+n.path:"",n.domain?";domain="+n.domain:"",n.secure?";secure":""].join("")}return exports.get=function(e){var t=function(e){if(0===e.indexOf('"'))e=e.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\");var a={};try{e=decodeURIComponent(e.replace(/\+/g," ")),$.each(r(e,";"),function(e,t){var n=r(t,"="),i=n[0],o=n[1];if(i)a[i]=o})}catch(e){}return a}(document.cookie);return"string"===$.type(e)?t[e]:t},exports.set=function(e,t,n){if($.isPlainObject(e))n=t,t=null;if(n=$.extend({},exports.defaultOptions,n),null===t)$.each(e,function(e,t){i(e,t,n)});else i(e,t,n)},exports.remove=function(e,t){(t=t||{}).expires=-1,i(e,"",$.extend({},exports.defaultOptions,t))},exports.defaultOptions={},exports}(cc_util_cookie),cc_util_life=function(exports){var n=cc_function_guid,i=cc_function_around,m=cc_function_extend,_=cc_function_nextTick,o=cc_function_toBoolean,r=cc_function_createEvent,c=cc_function_replaceWith,e=cc_util_event,a={},v="__update_async__";function t(l,p,f,d,h){return function(t,n,i){var o=this;if($.isPlainObject(t))return i=n,void $.each(t,function(e,t){o[f](e,t,i)});i=i||{};var a=o[d](t),e=o.constructor[l+"Validator"];if(e)if($.isFunction(e[t]))n=e[t].call(o,n,i);if($.isFunction(h))n=h(o,n,i);if(a!==n||i.force)if(o[p][t]=n,!i.silent){var r={};m(r,i),r.newValue=n,r.oldValue=a;var c={};c[t]=r;var u=function(e){if(e&&e[t])o.execute(e[t],[n,a,r])};if(u(o.inner("watchSync")),u(o.option("watchSync")),i.sync)return u(o.constructor[l+"Updater"]),u(o.option("watch")),void o.emit(l+"change",c);var s=o.inner(l+"Changes");if(!s)s={},o.inner(l+"Changes",s);if($.extend(s,c),!o.inner(v))o.inner(v,_(function(){o.sync(v)}))}}}var u={};function s(){this.error("initStruct() can just call one time.")}var l={initStruct:function(){var e=this,t=e.option("mainElement"),n=e.option("mainTemplate");if("string"===$.type(n)){var i,o=e.option("share"),a=e.type+n;if(o)t=u[a];if(!t){if(i=$(n),o)u[a]=i}else if(e.option("replace"))c(t,i=$(n));else t.html(n);if(i)t=i,e.option("mainElement",t)}var r=e.option("parentSelector");if(r&&!t.parent().is(r))t.appendTo(r);e.initStruct=s},warn:function(e){if("undefined"!=typeof console)console.warn(["[CC warn]",this.type,e].join(" "))},error:function(e){throw new Error(["[CC error]",this.type,e].join(" "))},live:function(e,t,n){var i=this.inner("main");if(i)i.on(e+this.namespace(),t,n);return this},emit:function(e,t){var n=this.option("context")||this;(e=r(e)).cc=n;var i=[e];if($.isPlainObject(t))i.push(t);e.type=e.type.toLowerCase();var o=n.get$();o.trigger.apply(o,i);var a="on"+e.type;if(!e.isPropagationStopped()&&!1===n.execute(a,i))e.preventDefault(),e.stopPropagation();return n.execute(a+"_",i),e},dispatch:function(e,t){if(!e.isPropagationStopped()){if(!e.originalEvent)e.originalEvent={preventDefault:$.noop,stopPropagation:$.noop};var n=$.Event(e);if(n.type="dispatch",this.emit(n,t),n.isPropagationStopped())e.preventDefault(),e.stopPropagation()}},before:function(e,t){return this.on("before"+e.toLowerCase(),t)},after:function(e,t){return this.on("after"+e.toLowerCase(),t)},find:function(e){var t=this.inner("main");if(t){var n=t.find(e);if(n.length)return n}},appendTo:function(e){var t=this.inner("main");if(t)t.appendTo(e)},prependTo:function(e){var t=this.inner("main");if(t)t.prependTo(e)},execute:function(e,t){var n=e;if("string"===$.type(e))n=this.option(e);if($.isFunction(n)){var i=this.option("context")||this;if($.isArray(t))return n.apply(i,t);else return n.call(i,t)}},renderWith:function(e,t,n){var i=this;if(!t)if(!(t=i.option("renderTemplate")))t=i.option("mainTemplate");if(!n)n=i.option("mainElement");var o,a=i.option("renderSelector");if(a)n=n.find(a);if($.isPlainObject(e)||$.isArray(e))o=i.execute("render",[e,t]);else if("string"===$.type(e))o=e;n.html(o)},namespace:function(){return"."+this.guid},option:function(e,t){var n=this;if(1===arguments.length&&"string"===$.type(e))return n.options[e];else{if($.isPlainObject(e))return void $.each(e,function(e,t){n.option(e,t)});n.options[e]=t}},inner:function(e,t){var n=this,i=n.inners||{};if(1===arguments.length&&"string"===$.type(e))return i[e];else{if($.isPlainObject(e))return void $.each(e,function(e,t){n.inner(e,t)});i[e]=t}},is:function(e){return this.states[e]},state:t("state","states","state","is",function(e,t){return o(t,!1)}),get:function(e){return this.properties[e]},set:t("property","properties","set","get")},p={sync:function(){var a=this,r=function(n,i,o){$.each(n,function(e,t){return a.execute(i[e],[t.newValue,t.oldValue,o?n:t])})};if($.each(["property","state"],function(e,t){var n=a.inner(t+"Changes");if(n){a.inner(t+"Changes",null);var i=a.constructor[t+"Updater"];if(i)r(n,i,!0);var o=a.option("watch");if(o)r(n,o);a.emit(t+"change",n)}}),arguments[0]!==v)a.execute(a.inner(v));a.inner(v,!1)},_sync:function(){if(!this.inner(v))return!1},_init:function(){var e="initCalled";if(this.is(e))return!1;this.state(e,!0)},_dispose:function(){var e="disposeCalled";if(this.is(e))return!1;this.state(e,!0)}};function f(e,t,n,i,o){var a,r=e["before"===i?"_"+t:t+"_"];if($.isFunction(r))if(!1!==(a=r.apply(e,n))&&!$.isPlainObject(a))a=null;if(!1===a)return!1;var c=!1;if(a&&a.dispatch)c=!0,delete a.dispatch;if((o=$.Event(o)).type=i+t,e.emit(o,a),c)e.dispatch(o,a);if(o.isDefaultPrevented())return!1;else return}return exports.extend=function(t,n){m(t,p),$.each(t,function(a,r){var e=a.indexOf("_");if($.isFunction(r)&&0!==e&&e!==a.length-1)if(!($.isArray(n)&&0<=$.inArray(a,n))){i(t,a,function(e){return f(this,a,arguments,"before",e)},function(e){var t=this,n=arguments,i=function(){return f(t,a,n,"after",e)};if(r.length+1===n.length){var o=n[n.length-1];if(o&&$.isFunction(o.then))return void o.then(i)}i()})}}),m(t,l),e.extend(t)},exports.init=function(t,e){if(!e)e={};return m(e,t.constructor.defaultOptions),e.onafterinit_=function(){t.state("inited",!0)},e.onafterdispose_=function(){t.state("disposed",!0),t.off();var e=t.inner("main");if(t.option("removeOnDispose")&&e)e.remove();_(function(){delete a[t.guid],t.properties=t.options=t.changes=t.states=t.inners=t.guid=null})},(a[t.guid=n()]=t).properties={},t.options=e,t.states={},t.inners={},t.init(),t},exports.dispose=function(e){e.sync();var t=e.inner("main")||e.option("mainElement");if(t)t.off(e.namespace())},exports}(cc_util_life),cc_util_supload_supload=function(exports){if(window.Supload===r)return window.Supload;var o=cc_util_cookie,a=cc_util_json;function r(e){$.extend(this,e),this.init()}r.prototype={constructor:r,init:function(){var e="_Supload_"+c++;this.movieName=e;var t=this.element;if("string"===$.type(t))t=document.getElementById(t);var n=this.data||(this.data={});$.each(o.get(),function(e,t){if("undefined"===$.type(n[e]))n[e]=t});var i=r.createSWF(e,this.flashUrl,this.getFlashVars());t.parentNode.replaceChild(i,t),this.element=i,r.instances[e]=this},getFlashVars:function(){var i=this,o=[];return $.each(["movieName","action","accept","multiple","fileName","data","header"],function(e,t){var n=i[t];if(null!=n){if($.isPlainObject(n))n=a.stringify(n);else if($.isArray(n))n=n.join(",");o.push(t+"="+encodeURIComponent(n))}}),o.push("projectName="+r.projectName),o.join("&amp;")},getFiles:function(){return this.element.getFiles&&this.element.getFiles()||[]},setAction:function(e){this.element.setAction&&this.element.setAction(e)},setData:function(e){this.element.setData&&this.element.setData(e)},reset:function(){this.element.reset&&this.element.reset()},upload:function(e){this.element.upload&&this.element.upload(e)},cancel:function(e){this.element.cancel&&this.element.cancel(e)},enable:function(){this.element.enable&&this.element.enable()},disable:function(){this.element.disable&&this.element.disable()},dispose:function(){this.element.dispose&&this.element.dispose(),r.instances[this.movieName]=null,window[this.movieName]=null}},r.projectName="Supload",r.createSWF=function(e,t,n){var i='<object id="'+e+'" class="'+r.projectName.toLowerCase()+'" type="application/x-shockwave-flash" data="'+t+'"><param name="movie" value="'+t+'" /><param name="allowscriptaccess" value="always" /><param name="wmode" value="transparent" /><param name="flashvars" value="'+n+'" /></object>';return $(i)[0]},r.instances={},r.STATUS_WAITING=0,r.STATUS_UPLOADING=1,r.STATUS_UPLOAD_SUCCESS=2,r.STATUS_UPLOAD_ERROR=3,r.ERROR_CANCEL=0,r.ERROR_SECURITY=1,r.ERROR_IO=2;var c=178245;return window.Supload=r}(),cc_helper_AjaxUploader=function(exports){var a=cc_function_ratio,r=cc_function_restrain,f=cc_function_nextTick,t=cc_util_life,e=cc_util_event,c=cc_util_mimeType;function d(e){t.init(this,e)}var n=d.prototype;n.type="AjaxUploader",n.init=function(){var e=this,t=e.option("mainElement");if(!t.is('input[type="file"]'))e.error('AjaxUploader mainElement must be <input type="file" />.');var n=$("<form></form>");t.replaceWith(n),n.append(t);var i,o,a={};if(e.option("accept"))a.accept=(i=e.option("accept"),o=[],$.each(i,function(e,t){o.push(c[t]||t)}),$.unique(o).join(","));if(e.option("multiple"))a.multiple=!0;t.prop(a).on("change"+e.namespace(),function(){$.each(e.getFiles(),function(e,t){t.dispose()}),e.inner("files",$.map(t.prop("files"),function(e,t){return new s({nativeFile:e,index:t})})),e.emit("filechange")}),e.inner({main:n,file:t,files:[]}),e.set({action:e.option("action"),data:e.option("data")}),e.emit("ready")},n.getFiles=function(){return this.inner("files")},n.reset=function(){this.inner("main")[0].reset()},n.upload=function(e,t){var n=this;if(!t)t=n.getFiles()[e];else t=new s(t),n.getFiles()[e]=t;if(t)if(t.upload({action:t.action||n.get("action"),data:n.get("data"),fileName:n.option("fileName"),header:n.option("header"),useChunk:n.option("useChunk"),chunkSize:n.option("chunkSize")})){var i=function(e,t){n.emit(e,t)};t.on("uploadstart",i).on("uploadprogress",i).on("uploadsuccess",i).on("uploaderror",i).on("uploadcomplete",i).on("chunkuploadsuccess",i)}},n.stop=function(e){var t=this.getFiles()[e];if(t)t.cancel()},n.enable=function(){this.inner("file").prop("disabled",!1)},n.disable=function(){this.inner("file").prop("disabled",!0)},n.dispose=function(){t.dispose(this),this.stop(),this.inner("file").off(this.namespace())},t.extend(n,["getFiles","setAction","setData"]),d.supportChunk="undefined"!=typeof FileReader,d.STATUS_WAITING=0,d.STATUS_UPLOADING=1,d.STATUS_UPLOAD_SUCCESS=2,d.STATUS_UPLOAD_ERROR=3,d.ERROR_CANCEL=0,d.ERROR_CHUNK_SIZE=-1;var h={uploadStart:{type:"loadstart",handler:function(e,t){e.status=d.STATUS_UPLOADING,e.emit("uploadstart",{fileItem:e.toPlainObject()})}},uploadSuccess:{type:"load",handler:function(e,t){var n={responseText:e.xhr.responseText},i=e.chunk;if(i){var o=e.file.size;if(i.uploaded<o){if(n.fileItem=e.toPlainObject(),!e.emit("chunkuploadsuccess",n).isDefaultPrevented())if(i.index++,i.uploaded+=i.uploading,i.uploaded<o)return void e.upload()}}e.status=d.STATUS_UPLOAD_SUCCESS,n.fileItem=e.toPlainObject(),e.emit("uploadsuccess",n),u(e)}},uploadError:{type:"error",handler:function(e,t,n){e.status=d.STATUS_UPLOAD_ERROR,e.emit("uploaderror",{fileItem:e.toPlainObject(),errorCode:n}),u(e)}},uploadStop:{type:"abort",handler:function(e,t){h.uploadError.handler(e,t,d.ERROR_CANCEL)}}},o={uploadProgress:{type:"progress",handler:function(e,t){var n=e.file.size,i=t.loaded,o=e.chunk;if(o){if(i>o.uploading)return void f(function(){h.uploadError.handler(e,{},d.ERROR_CHUNK_SIZE)});i+=o.uploaded}e.emit("uploadprogress",{fileItem:e.toPlainObject(),uploaded:i,total:n,percent:(100*r(a(i,n),0,1)).toFixed(2)+"%"})}}};function u(e){var n=e.xhr;if(n)$.each(h,function(e,t){n["on"+t.type]=null}),$.each(o,function(e,t){n.upload["on"+t.type]=null}),delete e.xhr;if(e.options)delete e.options;e.emit("uploadcomplete",{fileItem:e.toPlainObject()}),e.off()}function s(e){var t,n,i,o,a=this;if($.extend(a,e),null==a.file)a.file=(t=a.nativeFile,n=t.name,i=n.split("."),o=1<i.length?i.pop().toLowerCase():"",{name:n,type:o,size:t.size});if(null==a.status)a.status=d.STATUS_WAITING}var i=s.prototype;return i.upload=function(e){var n=this;if(!e)e=n.options;else n.options=e;var t=e.useChunk?d.STATUS_UPLOADING:d.STATUS_WAITING;if(!(n.status>t)){var i=new XMLHttpRequest;return n.xhr=i,$.each(h,function(e,t){i["on"+t.type]=function(e){t.handler(n,e)}}),$.each(o,function(e,t){i.upload["on"+t.type]=function(e){t.handler(n,e)}}),i.open("post",e.action,!0),f(function(){if(e.useChunk)n.uploadFileChunk(e);else n.uploadFile(e)}),!0}},i.uploadFile=function(e){var n=new FormData;if(e.data)$.each(e.data,function(e,t){n.append(e,t)});n.append(e.fileName,this.nativeFile);var i=this.xhr;if(e.header)$.each(e.header,function(e,t){i.setRequestHeader(e,t)});i.send(n)},i.uploadFileChunk=function(e){var t=this,n=t.nativeFile,i=t.file.size,o=t.chunk;if(!o)o=t.chunk={index:0,uploaded:0};var a=o.index,r=e.chunkSize,c=r*a,u=r*(a+1);if(i<u)u=i;if(o.uploading=u-c,!(o.uploading<=0)){var s=t.xhr,l={"Content-Type":"",X_FILENAME:encodeURIComponent(n.name),"Content-Range":"bytes "+(c+1)+"-"+u+"/"+i};if(e.header)$.extend(l,e.header);$.each(l,function(e,t){s.setRequestHeader(e,t)});var p=File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice;s.send(p.call(n,c,u))}else f(function(){h.uploadError.handler(t,{},d.ERROR_CHUNK_SIZE)})},i.cancel=function(){if(this.status===d.STATUS_UPLOADING)this.xhr.abort()},i.toPlainObject=function(){var e=this,t={index:e.index,file:e.file,nativeFile:e.nativeFile,status:e.status};if(e.chunk)t.chunk=e.chunk;return t},i.dispose=function(){this.cancel(),this.off()},e.extend(i),d}(),cc_helper_FlashUploader=function(exports){var r=cc_function_ucFirst,n=cc_function_ratio,t=cc_util_life,c=window.Supload;function e(e){t.init(this,e)}var i=e.prototype;i.type="FlashUploader",i.init=function(){var e=this,t=e.option("mainElement"),n=e.option("action"),i=e.option("data"),o={element:t[0],flashUrl:e.option("flashUrl"),action:i,accept:e.option("accept"),multiple:e.option("multiple"),data:i,header:e.option("header"),fileName:e.option("fileName"),customSettings:{uploader:e}};$.each(u,function(e,t){o["on"+r(e)]=t});var a=new c(o);e.inner({supload:a,watchSync:{action:function(e){a.setAction(e)},data:function(e){a.setData(e)}}}),e.set({action:n,data:i})},i.getFiles=function(){return this.inner("supload").getFiles()},i.reset=function(){this.inner("supload").reset()},i.upload=function(e){this.inner("supload").upload(e)},i.stop=function(e){this.inner("supload").cancel(e)},i.enable=function(){this.inner("supload").enable()},i.disable=function(){this.inner("supload").disable()},i.dispose=function(){t.dispose(this),this.inner("supload").dispose()},t.extend(i,["getFiles","setAction","setData"]),e.STATUS_WAITING=c.STATUS_WAITING,e.STATUS_UPLOADING=c.STATUS_UPLOADING,e.STATUS_UPLOAD_SUCCESS=c.STATUS_UPLOAD_SUCCESS,e.STATUS_UPLOAD_ERROR=c.STATUS_UPLOAD_ERROR,e.ERROR_CANCEL=c.ERROR_CANCEL,e.ERROR_SECURITY=c.ERROR_SECURITY,e.ERROR_IO=c.ERROR_IO;var u={ready:function(){this.customSettings.uploader.emit("ready")},fileChange:function(){this.customSettings.uploader.emit("filechange")},uploadStart:function(e){this.customSettings.uploader.emit("uploadstart",e)},uploadProgress:function(e){var t=this.customSettings.uploader;e.percent=(100*n(e.uploaded,e.total)).toFixed(2)+"%",t.emit("uploadprogress",e)},uploadSuccess:function(e){this.customSettings.uploader.emit("uploadsuccess",e)},uploadError:function(e){this.customSettings.uploader.emit("uploaderror",e)},uploadComplete:function(e){this.customSettings.uploader.emit("uploadcomplete",e)}};return e}(),cc_ui_Uploader="files"in $('<input type="file" />')[0]&&function(){if(!XMLHttpRequest)return!1;var e=new XMLHttpRequest;return"upload"in e&&"onprogress"in e.upload}()?cc_helper_AjaxUploader:cc_helper_FlashUploader,uploader_VideoUploader=function(exports){var o=cc_ui_Uploader,u=cc_util_url,c=cc_util_localStorage,a=cc_util_FiniteArray,t=1048576,n=1024*t,i=864e5,p=o.supportChunk,r={FlashUploader:{value:200*t,text:'你的浏览器最大支持上传 200M 的视频，可使用 <a href="http://www.baidu.com/s?wd=Chrome%20%E6%B5%8F%E8%A7%88%E5%99%A8" target="_blank">Chrome 浏览器</a> 获得 4G 超大文件上传体验！'},AjaxUploader:{value:4*n,text:"上传的视频不能超过 4G"}};function f(e){return encodeURIComponent(e.name)+"_"+e.size}var d,h="upload_retry",s="cdn_speed_test_time",l="cdn_speed_test_result";function m(e){d=e,c.set(l,e),c.set(s,$.now())}function _(e){$.extend(this,_.defaultOptions,e),this.init()}return _.prototype={init:function(){var s=this;if(s.useChunk)if(!p)s.useChunk=!1;var l=function(e){var t=e.fileItem,n=s.currentFiles[t.index];return $.extend(n.fileItem,t),n},r=new o({mainElement:s.mainElement,action:"",fileName:s.name,useChunk:s.useChunk,chunkSize:s.chunkSize,multiple:s.multiple,onfilechange:function(e){var t=r.getFiles();if(!(1<s.maxCount&&t.length>s.maxCount)){if(s.currentFiles=$.map(t,function(e){var t=e.file;return{fileItem:e,videoName:t.name,videoSize:t.size,videoType:t.type}}),$.isFunction(s.onFileChange))s.onFileChange()}else s.onError({content:"一次最多可上传"+s.maxCount+"个视频"})},onuploadstart:s.useChunk?function(e,t){var n=l(t);if(!n[h]){var i=n.fileItem,o=i.file,a=i.chunk.index;if(!o.size)a=0,r.stopFile(i.index);if(1)c.set(f(o),a+"|"+n.uploadId+"|"+$.now());if(!o.size){if($.isFunction(s.onUploadError))s.onUploadError();return void r.stop()}if($.isFunction(s.onUploadStart))s.onUploadStart(t)}}:null,onuploadprogress:function(e,t){var n=l(t),i=t.uploaded,o=$.now(),a=n.lastTime||n.uploadStartTime,r=i-n.uploaded,c=(o-a)/1e3;if(c<1)r*=1/c,c=1;var u=r/c;if(n.lastTime=o,n.uploaded=i,n.speeds.push(u),!n[h])if($.isFunction(s.onUploadProgress))s.onUploadProgress(t)},onchunkuploadsuccess:function(t,n){var i,o=l(n);try{i=$.parseJSON(n.responseText)}catch(e){}if($.isFunction(s.isChunkUploadSuccess)&&!s.isChunkUploadSuccess(i)){if(e.preventDefault(),o.chunkFailCount<s.chunkUploadMaxCount)o.chunkUploadTimer=setTimeout(function(){o.chunkUploadTimer=null,r.upload(o.fileItem)},s.chunkUploadInterval),o.chunkFailCount++;else if(s.stopFile(o.fileItem.index),$.isFunction(s.onChunkUploadError))s.onChunkUploadError(n)}else{if(s.retryUploadNextChunk(o))return void e.preventDefault();if($.isFunction(s.onChunkUploadSuccess))s.onChunkUploadSuccess(n)}},onuploadsuccess:function(e,t){var n,i=l(t);try{n=$.parseJSON(t.responseText)}catch(e){}if(!$.isFunction(s.isUploadSuccess)||s.isUploadSuccess(n)){if(!i[h]){if(s.useChunk)c.remove(f(i.fileItem.file));if($.isFunction(s.onUploadSuccess))t.uploadId=i.uploadId,s.onUploadSuccess(t)}}else if(s.stopFile(i.fileItem.index),$.isFunction(s.onUploadError))s.onUploadError(t)},onuploaderror:function(e,t){var n=l(t);if(n[h])delete n[h];var i=t.errorCode;if(i!==o.ERROR_CANCEL)if(i===o.ERROR_CHUNK_SIZE)alert("上传失败，请更换浏览器后再次尝试");else{if(n.uploaded&&d&&void 0!==d)m(void 0),n.fileItem.chunk.index=0,s.upload(n);if($.isFunction(s.onUploadError))s.onUploadError(t)}},onuploadcomplete:function(e,t){if(!l(t)[h])if($.isFunction(s.onUploadComplete))s.onUploadComplete(t)}});s.uploader=r},validateFiles:function(e){var n=this,i=!0;return $.each(e,function(e,t){if(!n.validateFile(t))return i=!1}),i},validateFile:function(e){var t,n=this;if(-1===$.inArray(e.videoType,n.accept))t="文件格式错误，请上传 "+n.accept.join("、")+" 等格式的视频";else if(n.maxSize){if(e.videoSize>n.maxSize)t=n.maxSizeError}else{var i=r[n.uploader.type];if(e.videoSize>i.value)t=i.text}if(!t){if(!$.isFunction(n.validateVideo)||n.validateVideo(e))return!0}else n.onError({content:t})},getUploadSpeed:function(e){var t=0,n=0,i=this.currentFiles[e];if(i&&i.speeds)i.speeds.each(function(e){t+=e,n++});return 0<n?parseFloat((t/n).toFixed(2),10):0},uploadFile:function(e){var t=this.uploader,n=e.fileItem;n.action=e.uploadUrl;var i=n.index;if(n.chunk){if(0===n.chunk.index)n.chunk.uploaded=0}else n=null;setTimeout(function(){if(n)n.status=o.STATUS_WAITING;t.upload(i,n)})},autoUpload:function(e){e.uploadStartTime=$.now(),e.chunkFailCount=0,e.uploaded=0,e.speeds=new a({max:5});var t=e.fileItem.file;if(this.useChunk){if(1){var n=c.get(f(t));if(n){var i=n.split("|"),o=i[2];if($.now()-o<this.resumeExpireTime)return e.uploadId=i[1],void this.resumeUpload(e)}}e.chunk=!0}this.upload(e)},upload:function(t){var n=this;n.getUploadUrl(t).then(function(e){t.fid=e.fid,t.uploadId=e.id,t.uploadUrl=e.url,n.uploadFile(t)},function(e){if(e&&e.code<0)n.onError({content:"上传失败，目前网络不可用"})})},retryUploadChunks:function(e,t){e[h]={chunks:t,index:0},e.fileItem.chunk.index=t[0],e.fileItem.chunk.uploaded=0,this.uploadFile(e)},retryUploadNextChunk:function(e){var t=e[h];if(t){var n=t.index+1;if(t.chunks[n])t.index=n,e.fileItem.chunk.index=t.chunks[n],e.fileItem.chunk.uploaded=0,this.uploadFile(e);else delete e[h];return!0}},resumeUpload:function(o){var a=this,r=a.resumeUploadMaxCount,c=0;if("number"!==$.type(r))throw new Error("[VideoUploader]resumeUploadMaxCount must be number.");!function i(){a.getResumeUploadUrl(o).then(function(e){if("id"in e){var t=e.uploaded||0,n=Math.floor(parseInt(t/a.chunkSize,10));o.fileItem.chunk={index:n,uploaded:t},o.fid=e.fid,o.uploadId=e.id,o.uploadUrl=e.url,o.cdnHost=u.parse(e.url).origin,a.uploadFile(o),c++}else{if(!c)a.waitResumeUpload();if(c<r)o.resumeUploadTimer=setTimeout(function(){o.resumeUploadTimer=null,i()},2e3);else a.cancelResumeUpload(o)}})}()},waitResumeUpload:function(){if($.isFunction(this.onResumeUploadWait))this.onResumeUploadWait()},cancelResumeUpload:function(e){if(e.resumeUploadTimer)clearTimeout(e.resumeUploadTimer),e.resumeUploadTimer=null;if(1)c.remove(f(e.fileItem.file));if($.isFunction(this.onResumeUploadCancel))this.onResumeUploadCancel()},stopFile:function(e){this.uploader.stop(e)},stop:function(){var t=this.uploader,e=this.currentFiles;$.each(e,function(e){t.stop(e)}),t.reset()},enable:function(){this.uploader.enable()},disable:function(){this.uploader.disable()},reset:function(){this.uploader.reset()},dispose:function(){$.each(this.currentFiles||[],function(e,t){if(t.resumeUploadTimer)clearTimeout(t.resumeUploadTimer),t.resumeUploadTimer=null;if(t.chunkUploadTimer)clearTimeout(t.chunkUploadTimer),t.chunkUploadTimer=null}),this.uploader.dispose()},destroy:function(){this.dispose()}},_.defaultOptions={name:"file",useChunk:!0,chunkUploadMaxCount:4,chunkUploadInterval:500,resumeUploadMaxCount:6,resumeUploadInterval:1e3,resumeExpireTime:.25*i,chunkSize:5*t,accept:["wmv","avi","dat","asf","rm","rmvb","ram","mpg","mpeg","3gp","mov","mp4","m4v","dvix","dv","mkv","flv","vob","qt","divx","cpk","fli","flc","mod"]},_.M=t,(window.BJY||(window.BJY={})).VideoUploader=_}()}();