<template>
    <div class="wrapper">
        <v-head></v-head>
        <v-sidebar2></v-sidebar2>
        <div class="content-box" :class="{'content-collapse':collapse}">
            <v-tags></v-tags>
            <div class="content">
                <transition name="move" mode="out-in">
                    <keep-alive :include="tagsList">
                        <router-view ></router-view>
                    </keep-alive>
                </transition>

            </div>
        </div>
    </div>
</template>

<script>
    import vHead from './Header.vue';
    import vSidebar from './Sidebar.vue';
    import vSidebar2 from './Sidebar2.vue';
    import vTags from './Tags.vue';
    import bus from './bus';
    export default {
        data(){
            return {
                tagsList: [],
                collapse: false,
                routerAlive:true
            }
        },
        components:{
            vHead, vSidebar,vSidebar2, vTags
        },
        created(){
            bus.$on('collapse', msg => {
                this.collapse = msg;
            })

            // 只有在标签页列表里的页面才使用keep-alive，即关闭标签之后就不保存到内存中了。
            bus.$on('tags', msg => {
                let arr = [];
                for(let i = 0, len = msg.length; i < len; i ++){
                    msg[i].name && arr.push(msg[i].name);

                }
                this.tagsList = arr;
            })
        },
        methods:{
            routerRefresh(){
      this.routerAlive = false;
    this.$nextTick(()=>{
      this.routerAlive = true;
    });
    }
        }
    }
</script>
