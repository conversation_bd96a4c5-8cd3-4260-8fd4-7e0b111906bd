/*
 * @description: 
 * @author: guo
 * @Date: 2021-09-08 11:04:03
 * @LastEditors: guo
 * @LastEditTime: 2021-09-28 10:16:47
 */
import axios from 'axios';
import { Message } from 'element-ui';

const request = axios.create({
  withCredentials: true,
  timeout: 60000,
  baseURL: `${window.location.origin}/`,
});

request.interceptors.request.use(config => {
  return config;
}, err => Promise.reject(err));

request.interceptors.response.use(res => {
  // console.log(res);
  return res.data
}, err => {
  if (err.message.includes('timeout')) {
    Message.error('抱歉，网络超时');
    return;
  }
  switch (err.response.status) {
    case 401:
      Message.error('登录过期');
      // store.commit('AUTH_ERROR');
      break;
    case 429:
      Message.error('操作频繁');
      break;
    case 500:
      Message.error('服务器错误');
      break;
    case 504:
      Message.error('请求超时');
      break;
    default:
      Message.error('未知错误');
      break;
  }
  Promise.reject(err);
});
export default request;
