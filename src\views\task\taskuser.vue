<template>
    <el-row>
        <div v-if="insertHtml1">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 学员任务管理</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col style="padding-bottom: 0px;">
                <el-form :inline="true">
                    <el-form-item class="handle-del mr10">
                        <el-input placeholder="输入用户名" v-model="condition.phone"></el-input>
                    </el-form-item >
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch()"><i class="el-icon-search"></i>搜索</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col>
                <el-table :data="tableData" border class="table">
                    <el-table-column prop="phone" label="用户名" width="120">
                    </el-table-column>
                    <el-table-column prop="real_name" label="昵称/姓名" width="120">
                    </el-table-column>
                    <el-table-column label="操作" width="200" align="center">
                        <template slot-scope="scope">
                            <el-button  size="mini" type="success" @click="manageTask(scope.row)">管理任务
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
            <el-col :span="24" class="toolbar">
                <my-page v-if="hackReset"  @repeatQry="search()" :pagecondition="condition" :resultData="resultData" :needCount=true></my-page>
            </el-col>
        </div>

        <!--任务管理-->
        <div class="container" v-if="editHtml">
            <el-col class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-tickets"></i> 学员任务管理</el-breadcrumb-item>
                    <el-breadcrumb-item>任务详情</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <div class="form-box">
                <el-form :inline="true">
                    <el-form-item>
                        <el-button type="primary" @click="handleAddTask()">新增任务</el-button>
                        <el-button @click="closeEdit">返 回</el-button>
                    </el-form-item>
                </el-form>
                <el-table :data="tableData2" border class="table">
                    <el-table-column prop="planDate" label="时间" width="120">
                    </el-table-column>
                    <el-table-column prop="planText" label="任务内容" width="120">
                    </el-table-column>
                    <el-table-column label="操作" width="200" align="center">
                        <template slot-scope="scope">
                            <el-button  size="mini" type="success" @click="deleUserTask(scope.row)">删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-col>
                    <el-dialog title="新增任务" :visible.sync="isShowAddTaskVisible">
                        <el-form label-width="80px" :model="temp" ref="dataForm">
                            <el-form-item label="日期" prop="planDate">
                                <el-date-picker type="date"  v-model="temp.planDate"
                                                style="width:200px"
                                                value-format="yyyy-MM-dd"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="任务内容" prop="planText">
                                <el-input type="textarea" v-model="temp.planText"></el-input>
                            </el-form-item>
                        </el-form>
                        <div slot="footer" class="dialog-footer">
                            <el-button @click="isShowAddTaskVisible = false">取消</el-button>
                            <el-button type="primary" :loading="listLoading" @click="addUserTask()"   class="title1">确定</el-button>
                        </div>
                    </el-dialog>
                </el-col>
            </div>
        </div>
    </el-row>
</template>

<script>
    export default {
        name: "campus",
        data() {
            return {
                condition:{},
                resultData:{},
                hackReset:true,
                isShowAddTaskVisible: false,
                tableData: [],
                tableData2: [],
                temp:{},
                userId:"",
                listLoading: false,
                isShowAddVisible: false,
                insertHtml1:true,
                editHtml:false,
            }
        },
        created() {
            this.search();
        },
        methods: {
            handleSearch() {
                this.hackReset = false
                this.$nextTick(() => {
                    this.hackReset = true
                    this.search();
                })
            },
            search(){
                let params = this.condition;
                this.url = '/mgr/individual/getIndividualList';
                this.$axios.post(this.url,params)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.resultData=res.data.body;
                            this.tableData = res.data.body.list;
                        } else {
                            this.$message.error('获取失败');
                        }
                    })
            },
            manageTask(row){
                this.insertHtml1=false;
                this.editHtml=true;
                this.userId=row.userId;
                this.seachUserTask()
            },
            seachUserTask(){
                this.url = '/mgr/taskplan/getUserTaskPlan';
                this.$axios.get(this.url,{
                    "userId":this.userId
                })
                    .then((res) => {
                        if (res.state === "success") {
                            this.tableData2 = res.body;
                        } else {
                            this.$message.error('获取失败');
                        }
                    })
            },
            handleAddTask(){
                this.temp={};
                this.isShowAddTaskVisible=true;
            },
            addUserTask(){
                const tempData = Object.assign({}, this.temp);
                tempData.userId=this.userId;
                this.url = '/mgr/taskplan/addPlanText';
                this.$axios.post(this.url,tempData)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.isShowAddTaskVisible=false;
                            this.seachUserTask();
                            this.$message.success(`成功`);
                        } else {
                            this.$message.error('失败');
                        }
                    })
            },
            deleUserTask(row){
                const tempData = Object.assign({}, row);
                this.url = '/mgr/taskplan/deleteTaskPlan';
                this.$axios.post(this.url,tempData)
                    .then((res) => {
                        if (res.data.state === "success") {
                            this.seachUserTask();
                            this.$message.success(`成功`);
                        } else {
                            this.$message.error('失败');
                        }
                    })
            },
            closeEdit(){
                this.editHtml = false;
                this.insertHtml1=true;
                this.handleSearch();
            },
            reset(){
                this.condition={};
                this.temp={}
            }
        }
    }
</script>

<style scoped>

</style>
